# MatchIQ 🏥

A sophisticated caregiver-client matching platform built with Swift Vapor. MatchIQ uses intelligent compatibility algorithms to connect clients with the most suitable caregivers based on comprehensive questionnaire responses.

## 🌟 Features

### Core Functionality
- **Intelligent Matching Algorithm** - Advanced compatibility scoring across multiple categories
- **Bidirectional Match Viewing** - Both clients and caregivers can view potential matches
- **Interactive Match Confirmation** - Clients can confirm or reject caregiver matches
- **Comprehensive Questionnaires** - Multi-category assessment for accurate matching
- **Real-time Match Calculation** - Dynamic compatibility scoring with caching

### User Experience
- **Beautiful Web Interface** - Modern, responsive design with gradient banners
- **Mobile-Optimized** - Touch-friendly interface that works on all devices
- **Keyboard Navigation** - Arrow key support for browsing matches
- **Status Tracking** - Visual indicators for match confirmation status
- **Empty State Handling** - Informative displays when no matches are available

### Technical Features
- **PostgreSQL Database** - Robust data persistence with Fluent ORM
- **RESTful API** - Clean API endpoints for all operations
- **Server-Side Rendering** - Leaf templating for fast, SEO-friendly pages
- **CORS Support** - Cross-origin resource sharing for API access
- **Auto-Migration** - Database schema management and seeding

## 🚀 Quick Start

### Prerequisites
- Swift 6.0+
- PostgreSQL database
- macOS 13+ (for development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd MatchIQ
   ```

2. **Set up PostgreSQL database**
   ```bash
   createdb matchiq
   ```

3. **Configure environment variables** (optional)
   ```bash
   export DATABASE_HOST=localhost
   export DATABASE_PORT=5432
   export DATABASE_USERNAME=your_username
   export DATABASE_PASSWORD=your_password
   export DATABASE_NAME=matchiq
   ```

4. **Build and run**
   ```bash
   swift build
   swift run
   ```

5. **Access the application**
   - Web Interface: `http://localhost:8080`
   - API Base URL: `http://localhost:8080`

## 🏗️ Architecture

### Technology Stack
- **Backend**: Swift Vapor 4.110.1+
- **Database**: PostgreSQL with Fluent ORM
- **Frontend**: Server-side rendered HTML with Leaf templating
- **Styling**: Bootstrap 5 with custom CSS
- **JavaScript**: Vanilla JS for interactive features

### Project Structure
```
MatchIQ/
├── Sources/MatchIQ/
│   ├── Controllers/           # API Controllers
│   ├── LeafControllers/       # Web Controllers
│   ├── Models/               # Database Models
│   ├── Migrations/           # Database Migrations
│   ├── Services/             # Business Logic
│   └── configure.swift       # App Configuration
├── Resources/Views/          # Leaf Templates
├── Public/                   # Static Assets
└── Tests/                    # Test Files
```

## 📊 Database Schema

### Core Models
- **Client** - Client profiles and information
- **Caregiver** - Caregiver profiles and qualifications
- **Question** - Questionnaire questions by category
- **AnswerOption** - Available answer choices
- **ClientResponse** - Client questionnaire responses
- **CaregiverResponse** - Caregiver questionnaire responses
- **MatchResult** - Calculated compatibility scores
- **MatchConfirmation** - Client match decisions

### Compatibility Categories
1. **Personality & Communication** - Communication style, personality traits
2. **Care Needs & Skills** - Medical needs, care specializations
3. **Lifestyle & Interests** - Hobbies, lifestyle preferences
4. **Cultural & Language** - Cultural background, language preferences
5. **Logistics & Schedule** - Availability, location, scheduling

## 🎯 Matching Algorithm

### Compatibility Scoring
The matching algorithm evaluates compatibility across five key categories:

1. **Question Categorization** - Questions are grouped by compatibility category
2. **Response Comparison** - Client and caregiver responses are compared
3. **Weighted Scoring** - Each category contributes to overall compatibility
4. **Percentage Calculation** - Final score expressed as percentage match
5. **Result Caching** - Matches are stored to prevent recalculation

### Match Flow
```
Client Registration → Questionnaire → Match Calculation → View Matches → Confirm/Reject
Caregiver Registration → Questionnaire → Appear in Client Matches → View Client Matches
```

## 🎨 User Interface

### Design Principles
- **Clean & Modern** - Minimalist design with focus on usability
- **Mobile-First** - Responsive design that works on all screen sizes
- **Accessible** - High contrast, readable typography, keyboard navigation
- **Consistent** - Unified design language across all pages

### Key Components
- **Match Banners** - Beautiful gradient headers showing match percentage
- **Navigation Controls** - Previous/Next buttons with keyboard support
- **Status Indicators** - Visual feedback for match confirmation status
- **Responsive Cards** - Information cards that adapt to screen size

## 🔧 Configuration

### Database Configuration
The application uses PostgreSQL with the following default settings:
- Host: `localhost`
- Port: `5432`
- Database: `matchiq`
- Username: Configurable via environment
- Password: Configurable via environment

### Environment Variables
```bash
DATABASE_HOST=localhost          # Database host
DATABASE_PORT=5432              # Database port
DATABASE_USERNAME=username      # Database username
DATABASE_PASSWORD=password      # Database password
DATABASE_NAME=matchiq           # Database name
```

## 🧪 Testing

### Running Tests
```bash
swift test
```

### Test Coverage
- Model validation tests
- API endpoint tests
- Matching algorithm tests
- Database migration tests

## 📈 Performance

### Optimization Features
- **Match Result Caching** - Prevents duplicate calculations
- **Database Indexing** - Optimized queries for fast lookups
- **Lazy Loading** - Efficient data loading strategies
- **Static Asset Optimization** - Minified CSS and JavaScript

### Scalability Considerations
- Stateless design for horizontal scaling
- Database connection pooling
- Efficient query patterns
- Caching strategies for frequently accessed data

## 🔒 Security

### Security Features
- **Input Validation** - Server-side validation of all inputs
- **SQL Injection Prevention** - Parameterized queries via Fluent ORM
- **CORS Configuration** - Controlled cross-origin access
- **Error Handling** - Secure error messages without information leakage

## 🚀 Deployment

### Heroku Deployment (Recommended)

#### Quick Deploy
[![Deploy](https://www.herokucdn.com/deploy/button.svg)](https://heroku.com/deploy)

#### Manual Deployment
1. **Install Heroku CLI**
   ```bash
   # macOS
   brew tap heroku/brew && brew install heroku

   # Or download from https://devcenter.heroku.com/articles/heroku-cli
   ```

2. **Create and Deploy**
   ```bash
   heroku login
   heroku create your-app-name
   heroku addons:create heroku-postgresql:essential-0
   git push heroku main
   heroku run vapor run migrate --yes
   heroku open
   ```

3. **Environment Configuration**
   ```bash
   heroku config:set SWIFT_VERSION=6.0
   heroku config:set VAPOR_ENV=production
   ```

### Traditional Server Deployment
1. **Environment Setup**
   ```bash
   export ENVIRONMENT=production
   export DATABASE_URL=postgresql://user:pass@host:port/db
   ```

2. **Build for Production**
   ```bash
   swift build -c release
   ```

3. **Run Production Server**
   ```bash
   .build/release/MatchIQ serve --env production --hostname 0.0.0.0 --port 8080
   ```

### Docker Deployment
```dockerfile
FROM swift:6.0
WORKDIR /app
COPY . .
RUN swift build -c release
EXPOSE 8080
CMD [".build/release/MatchIQ", "serve", "--env", "production", "--hostname", "0.0.0.0", "--port", "8080"]
```

### Deployment Files
- `Procfile` - Heroku process configuration
- `app.json` - Heroku app metadata and one-click deploy
- `.swift-version` - Swift version specification
- `DEPLOY.md` - Detailed deployment guide

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Code Style
- Follow Swift API Design Guidelines
- Use meaningful variable and function names
- Add documentation for public APIs
- Maintain test coverage

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### Getting Help
- Check the documentation above
- Review the code comments
- Open an issue for bugs or feature requests
- Contact the development team

### Common Issues
- **Database Connection**: Ensure PostgreSQL is running and credentials are correct
- **Port Conflicts**: Check if port 8080 is available
- **Migration Errors**: Verify database permissions and schema

---

**MatchIQ** - Connecting care with intelligence. 🏥💙

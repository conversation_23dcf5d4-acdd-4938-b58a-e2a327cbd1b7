#!/bin/bash

# MatchIQ Seed Data Script
# This script helps you populate your database with sample data

echo "🌱 MatchIQ Database Seeder"
echo "=========================="
echo ""

# Check if we should clear existing data
read -p "Do you want to clear existing data first? (y/N): " clear_data
echo ""

# Build the command
if [[ $clear_data =~ ^[Yy]$ ]]; then
    echo "⚠️  This will delete ALL existing clients and caregivers!"
    read -p "Are you sure? (y/N): " confirm_clear
    echo ""
    
    if [[ $confirm_clear =~ ^[Yy]$ ]]; then
        echo "🗑️  Clearing existing data and seeding new data..."
        swift run App seed --clear
    else
        echo "❌ Cancelled."
        exit 0
    fi
else
    echo "📊 Seeding data (keeping existing data)..."
    swift run App seed
fi

echo ""
echo "✅ Seeding completed!"
echo ""
echo "📈 Summary:"
echo "   • 30 sample clients created"
echo "   • 30 sample caregivers created"
echo "   • Each with realistic questionnaire responses"
echo ""
echo "🌐 You can now view them at:"
echo "   • Clients: http://127.0.0.1:8080/clients"
echo "   • Caregivers: http://127.0.0.1:8080/caregivers"
echo ""

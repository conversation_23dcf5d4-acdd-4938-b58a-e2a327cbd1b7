# MatchIQ Seed Data

This document explains how to populate your MatchIQ database with realistic sample data for testing and development.

## Quick Start

### Option 1: Use the Easy Script
```bash
./seed.sh
```
This interactive script will guide you through the seeding process.

### Option 2: Direct Commands

#### Seed Everything (Recommended)
```bash
swift run App seed
```

#### Clear and Reseed Everything
```bash
swift run App seed --clear
```

#### Seed Only Clients
```bash
swift run App seed --clients
```

#### Seed Only Caregivers
```bash
swift run App seed --caregivers
```

#### Clear Data and Seed Only Clients
```bash
swift run App seed --clear --clients
```

## What Gets Created

### Clients (30 total)
- **Names**: Realistic names like "<PERSON>", "<PERSON>", "<PERSON>"
- **Responses**: Varied answers to all questionnaire questions including:
  - Care preferences (Personal Care, Companionship, etc.)
  - Schedule preferences (Morning, Afternoon, Evening, Flexible)
  - Location preferences (Downtown, Suburbs, various neighborhoods)
  - Special needs (<PERSON><PERSON>ia care, Mobility assistance, etc.)
  - Budget preferences ($15-35+/hour ranges)

### Caregivers (30 total)
- **Names**: Professional-sounding names like "<PERSON>", "<PERSON>"
- **Responses**: Complementary answers to match with clients:
  - Experience levels (0-1 years to 10+ years)
  - Service offerings matching client needs
  - Availability that overlaps with client preferences
  - Geographic coverage
  - Specializations and certifications

## Sample Data Categories

### Care Types
- Personal Care
- Medication Management
- Companionship
- Light Housekeeping
- Meal Preparation
- Transportation

### Schedule Options
- Morning (6 AM - 12 PM)
- Afternoon (12 PM - 6 PM)
- Evening (6 PM - 12 AM)
- Flexible scheduling
- Weekdays/Weekends only
- 24/7 availability

### Locations
- Downtown
- Suburbs
- North/South/East/West Side
- Multiple area coverage

### Experience Levels
- New to caregiving
- 1-3 years experience
- 3-5 years experience
- 5-10 years experience
- 10+ years experience
- Professional certifications
- Healthcare background

### Special Needs
- Dementia/Alzheimer's care
- Mobility assistance
- Diabetes management
- Post-surgery recovery
- Mental health support
- Physical therapy assistance
- Stroke recovery
- Chronic pain management

## Testing the Matching System

After seeding, you can test the matching functionality:

1. **View All Clients**: http://127.0.0.1:8080/clients
2. **View All Caregivers**: http://127.0.0.1:8080/caregivers
3. **Test Matching**: Click on any client to see their potential caregiver matches
4. **Test Caregiver View**: Click on any caregiver to see their potential client matches

## Data Quality

The seed data is designed to create realistic matching scenarios:
- **High Match Potential**: Some clients and caregivers have overlapping preferences
- **Varied Responses**: Different combinations to test edge cases
- **Realistic Names**: Professional and diverse naming
- **Balanced Distribution**: Even spread across different preferences and needs

## Clearing Data

⚠️ **Warning**: The `--clear` flag will delete ALL existing clients, caregivers, and their responses. Use with caution in production environments.

## Troubleshooting

### "No questions found" Error
If you see this error, make sure the questions have been seeded first:
```bash
swift run App migrate
```

### Database Connection Issues
Ensure your database is running and the connection settings in `configure.swift` are correct.

### Permission Issues with seed.sh
Make the script executable:
```bash
chmod +x seed.sh
```

## Customization

To modify the seed data:
1. Edit `Sources/App/SeedData.swift` to change names or response options
2. Edit `Sources/App/Commands/SeedCommand.swift` to modify the seeding logic
3. Rebuild and run: `swift run App seed --clear`

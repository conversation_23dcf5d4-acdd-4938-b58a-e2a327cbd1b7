{"originHash": "a267a45a84de8521ba3abb68d53529c7348d6b8ada7f4ed095dabf1765b2b48d", "pins": [{"identity": "async-http-client", "kind": "remoteSourceControl", "location": "https://github.com/swift-server/async-http-client.git", "state": {"revision": "333f51104b75d1a5b94cb3b99e4c58a3b442c9f7", "version": "1.25.2"}}, {"identity": "async-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/async-kit.git", "state": {"revision": "e048c8ee94967e8d8a1c2ec0e1156d6f7fa34d31", "version": "1.20.0"}}, {"identity": "console-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/console-kit.git", "state": {"revision": "742f624a998cba2a9e653d9b1e91ad3f3a5dff6b", "version": "4.15.2"}}, {"identity": "fluent", "kind": "remoteSourceControl", "location": "https://github.com/vapor/fluent.git", "state": {"revision": "223b27d04ab2b51c25503c9922eecbcdf6c12f89", "version": "4.12.0"}}, {"identity": "fluent-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/fluent-kit.git", "state": {"revision": "1385c48f98e1e44335b251cc11dc11a0a50ab853", "version": "1.52.1"}}, {"identity": "fluent-postgres-driver", "kind": "remoteSourceControl", "location": "https://github.com/vapor/fluent-postgres-driver.git", "state": {"revision": "095bc5a17ab3363167f4becb270b6f8eb790481c", "version": "2.10.1"}}, {"identity": "leaf", "kind": "remoteSourceControl", "location": "https://github.com/vapor/leaf.git", "state": {"revision": "d469584b9186851c5a4012d11325fb9db3207ebb", "version": "4.5.0"}}, {"identity": "leaf-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/leaf-kit.git", "state": {"revision": "cf186d8f2ef33e16fd1dd78df36466c22c2e632f", "version": "1.13.1"}}, {"identity": "multipart-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/multipart-kit.git", "state": {"revision": "3498e60218e6003894ff95192d756e238c01f44e", "version": "4.7.1"}}, {"identity": "postgres-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/postgres-kit.git", "state": {"revision": "f4d4b9e8db9a907644d67d6a7ecb5f0314eec1ad", "version": "2.14.0"}}, {"identity": "postgres-nio", "kind": "remoteSourceControl", "location": "https://github.com/vapor/postgres-nio.git", "state": {"revision": "5d817be55cae8b00003b7458944954558302d006", "version": "1.25.0"}}, {"identity": "routing-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/routing-kit.git", "state": {"revision": "8c9a227476555c55837e569be71944e02a056b72", "version": "4.9.1"}}, {"identity": "sql-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/sql-kit.git", "state": {"revision": "baf0d8684a43f16cd11ebcc67300c8ab5cb5d078", "version": "3.33.0"}}, {"identity": "swift-algorithms", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-algorithms.git", "state": {"revision": "87e50f483c54e6efd60e885f7f5aa946cee68023", "version": "1.2.1"}}, {"identity": "swift-asn1", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-asn1.git", "state": {"revision": "ae33e5941bb88d88538d0a6b19ca0b01e6c76dcf", "version": "1.3.1"}}, {"identity": "swift-async-algorithms", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-async-algorithms.git", "state": {"revision": "4c3ea81f81f0a25d0470188459c6d4bf20cf2f97", "version": "1.0.3"}}, {"identity": "swift-atomics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-atomics.git", "state": {"revision": "cd142fd2f64be2100422d658e7411e39489da985", "version": "1.2.0"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "671108c96644956dddcd89dd59c203dcdb36cec7", "version": "1.1.4"}}, {"identity": "swift-crypto", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-crypto.git", "state": {"revision": "e8d6eba1fef23ae5b359c46b03f7d94be2f41fed", "version": "3.12.3"}}, {"identity": "swift-distributed-tracing", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-distributed-tracing.git", "state": {"revision": "a64a0abc2530f767af15dd88dda7f64d5f1ff9de", "version": "1.2.0"}}, {"identity": "swift-http-structured-headers", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-http-structured-headers.git", "state": {"revision": "8e769facea6b7d46ea60e5e93635a384fd573480", "version": "1.2.1"}}, {"identity": "swift-http-types", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-http-types.git", "state": {"revision": "a0a57e949a8903563aba4615869310c0ebf14c03", "version": "1.4.0"}}, {"identity": "swift-log", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-log.git", "state": {"revision": "3d8596ed08bd13520157f0355e35caed215ffbfa", "version": "1.6.3"}}, {"identity": "swift-metrics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-metrics.git", "state": {"revision": "44491db7cc66774ab930cf15f36324e16b06f425", "version": "2.6.1"}}, {"identity": "swift-nio", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio.git", "state": {"revision": "c51907a839e63ebf0ba2076bba73dd96436bd1b9", "version": "2.81.0"}}, {"identity": "swift-nio-extras", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio-extras.git", "state": {"revision": "00f3f72d2f9942d0e2dc96057ab50a37ced150d4", "version": "1.25.0"}}, {"identity": "swift-nio-http2", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio-http2.git", "state": {"revision": "170f4ca06b6a9c57b811293cebcb96e81b661310", "version": "1.35.0"}}, {"identity": "swift-nio-ssl", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio-ssl.git", "state": {"revision": "0cc3528ff48129d64ab9cab0b1cd621634edfc6b", "version": "2.29.3"}}, {"identity": "swift-nio-transport-services", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio-transport-services.git", "state": {"revision": "3c394067c08d1225ba8442e9cffb520ded417b64", "version": "1.23.1"}}, {"identity": "swift-numerics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-numerics.git", "state": {"revision": "e0ec0f5f3af6f3e4d5e7a19d2af26b481acb6ba8", "version": "1.0.3"}}, {"identity": "swift-service-context", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-service-context.git", "state": {"revision": "8946c930cae601452149e45d31d8ddfac973c3c7", "version": "1.2.0"}}, {"identity": "swift-service-lifecycle", "kind": "remoteSourceControl", "location": "https://github.com/swift-server/swift-service-lifecycle.git", "state": {"revision": "7ee57f99fbe0073c3700997186721e74d925b59b", "version": "2.7.0"}}, {"identity": "swift-system", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-system.git", "state": {"revision": "a34201439c74b53f0fd71ef11741af7e7caf01e1", "version": "1.4.2"}}, {"identity": "vapor", "kind": "remoteSourceControl", "location": "https://github.com/vapor/vapor.git", "state": {"revision": "87b0edd2633c35de543cb7573efe5fbf456181bc", "version": "4.114.1"}}, {"identity": "websocket-kit", "kind": "remoteSourceControl", "location": "https://github.com/vapor/websocket-kit.git", "state": {"revision": "4232d34efa49f633ba61afde365d3896fc7f8740", "version": "2.15.0"}}], "version": 3}
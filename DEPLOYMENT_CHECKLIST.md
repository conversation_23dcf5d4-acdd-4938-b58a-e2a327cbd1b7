# MatchIQ Heroku Deployment Checklist ✅

## Pre-Deployment Checklist

### ✅ Code Preparation
- [x] All code committed to Git repository
- [x] Build passes locally (`swift build`)
- [x] Tests pass (`swift test`)
- [x] No sensitive data in code (passwords, API keys)
- [x] Environment variables properly configured
- [x] Database configuration supports both local and Heroku

### ✅ Heroku Configuration Files
- [x] `Procfile` - Process configuration
- [x] `app.json` - App metadata and one-click deploy
- [x] `.swift-version` - Swift version specification
- [x] `.gitignore` - Excludes build artifacts and sensitive files

### ✅ Database Setup
- [x] PostgreSQL configuration in `configure.swift`
- [x] Support for `DATABASE_URL` environment variable
- [x] Migrations properly configured
- [x] Auto-migration disabled in production
- [x] Seed data included in migrations

## Deployment Steps

### 1. Heroku Account Setup
```bash
# Install Heroku CLI
brew tap heroku/brew && brew install heroku

# Login to Heroku
heroku login
```

### 2. Create Heroku App
```bash
# Create new app (replace 'your-app-name' with desired name)
heroku create your-app-name

# Or use auto-generated name
heroku create
```

### 3. Add PostgreSQL Database
```bash
# Add PostgreSQL addon
heroku addons:create heroku-postgresql:essential-0

# Verify addon was added
heroku addons
```

### 4. Configure Environment
```bash
# Set Swift version
heroku config:set SWIFT_VERSION=6.0

# Set Vapor environment
heroku config:set VAPOR_ENV=production

# Verify configuration
heroku config
```

### 5. Deploy Application
```bash
# Add all files to git
git add .

# Commit changes
git commit -m "Deploy to Heroku"

# Push to Heroku (this triggers build and deployment)
git push heroku main
```

### 6. Run Database Migrations
```bash
# Run migrations to set up database schema
heroku run vapor run migrate --yes

# Verify migration completed successfully
heroku logs --tail
```

### 7. Test Deployment
```bash
# Open app in browser
heroku open

# Check app status
heroku ps

# View logs
heroku logs --tail
```

## Post-Deployment Verification

### ✅ Application Health
- [ ] App loads successfully in browser
- [ ] Health check endpoint responds: `/health`
- [ ] Home page displays correctly
- [ ] No error logs in Heroku dashboard

### ✅ Database Functionality
- [ ] Client registration works
- [ ] Caregiver registration works
- [ ] Questionnaire submission works
- [ ] Match calculation works
- [ ] Match viewing works

### ✅ Core Features
- [ ] Client can register and complete questionnaire
- [ ] Caregiver can register and complete questionnaire
- [ ] Clients can view caregiver matches
- [ ] Caregivers can view client matches
- [ ] Match confirmation system works
- [ ] Navigation between matches works

### ✅ Performance & Monitoring
- [ ] App responds within reasonable time
- [ ] No memory leaks or excessive resource usage
- [ ] Database queries perform well
- [ ] Static assets load correctly

## Troubleshooting Common Issues

### Build Failures
```bash
# Check build logs
heroku logs --tail

# Common fixes:
# 1. Verify Swift version in .swift-version
# 2. Check Package.swift dependencies
# 3. Ensure all files are committed to git
```

### Database Issues
```bash
# Check database status
heroku pg:info

# Reset database (WARNING: destroys all data)
heroku pg:reset DATABASE_URL --confirm your-app-name
heroku run vapor run migrate --yes

# Check database logs
heroku logs --tail | grep postgres
```

### App Won't Start
```bash
# Check process status
heroku ps

# Restart app
heroku restart

# Check configuration
heroku config

# Verify Procfile is correct
cat Procfile
```

## Monitoring & Maintenance

### Regular Monitoring
```bash
# Check app metrics
heroku logs --tail

# Monitor database usage
heroku pg:info

# Check dyno usage
heroku ps
```

### Scaling
```bash
# Scale web dynos
heroku ps:scale web=1

# Upgrade database plan
heroku addons:upgrade heroku-postgresql:standard-0
```

### Backup
```bash
# Create manual backup
heroku pg:backups:capture

# List backups
heroku pg:backups

# Download backup
heroku pg:backups:download
```

## Success Criteria

✅ **Deployment Successful When:**
- App is accessible via Heroku URL
- All core functionality works
- Database is properly configured
- No critical errors in logs
- Performance is acceptable

## Next Steps After Deployment

1. **Custom Domain** (Optional)
   ```bash
   heroku domains:add www.yourdomain.com
   ```

2. **SSL Certificate** (Automatic with custom domain)

3. **Monitoring Setup**
   - Set up log monitoring
   - Configure alerts for errors
   - Monitor performance metrics

4. **CI/CD Pipeline** (Optional)
   - Set up GitHub Actions
   - Automatic deployment on push
   - Automated testing

5. **Production Optimizations**
   - Upgrade to paid dynos for no sleeping
   - Upgrade database plan for better performance
   - Set up CDN for static assets

## Support Resources

- **Heroku Documentation**: https://devcenter.heroku.com
- **Vapor Documentation**: https://docs.vapor.codes
- **PostgreSQL on Heroku**: https://devcenter.heroku.com/articles/heroku-postgresql
- **Swift Buildpack**: https://github.com/vapor-community/heroku-buildpack

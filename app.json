{"name": "MatchIQ", "description": "A sophisticated caregiver-client matching platform built with Swift Vapor", "repository": "https://github.com/your-username/MatchIQ", "logo": "https://cdn.vapor.codes/images/vapor-logo.png", "keywords": ["swift", "vapor", "caregiver", "matching", "healthcare"], "image": "heroku/heroku:22", "stack": "heroku-22", "buildpacks": [{"url": "https://github.com/vapor/heroku-buildpack.git"}], "formation": {"web": {"quantity": 1, "size": "basic"}}, "addons": [{"plan": "heroku-postgresql:essential-0"}], "env": {"SWIFT_VERSION": {"description": "Swift version to use", "value": "5.10"}, "VAPOR_ENV": {"description": "Vapor environment", "value": "production"}, "SWIFT_BUILD_CONFIGURATION": {"description": "Swift build configuration", "value": "release"}}, "scripts": {"postdeploy": ".build/release/MatchIQ migrate --yes"}}
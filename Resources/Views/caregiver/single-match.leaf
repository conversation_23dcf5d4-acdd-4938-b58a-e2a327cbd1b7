#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport

    #export("content"):
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-10 col-xl-8">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="fw-bold mb-1">Client Match</h1>
                            <p class="text-muted mb-0">Match #(currentIndex + 1) of #(totalMatches)</p>
                        </div>
                        <a href="/caregiver/#(caregiver.id)" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Profile
                        </a>
                    </div>

                    <!-- Match Banner -->
                    <div class="match-banner mb-4">
                        <div class="match-banner-content">
                            <div class="match-banner-info">
                                <div class="match-banner-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="match-banner-text">
                                    <h2 class="match-banner-name">#(client.name)</h2>
                                    <p class="match-banner-subtitle">Potential Client Match</p>
                                </div>
                            </div>
                            <div class="match-banner-percentage">
                                <span class="match-percentage-value">#(match.matchPercentage)%</span>
                                <span class="match-percentage-label">Match</span>
                            </div>
                        </div>
                    </div>

                    <!-- Match Card -->
                    <div class="card shadow-lg border-0 mb-4">

                        <div class="card-body p-4">
                            <!-- Compatibility Breakdown -->
                            <h4 class="mb-4">
                                <i class="fas fa-chart-bar text-primary me-2"></i>
                                Compatibility Breakdown
                            </h4>

                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <div class="compatibility-item">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-semibold">Personality & Communication</span>
                                            <span class="badge bg-primary">#(match.categoryScores.personalityCommunication)%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-primary" style="width: #(match.categoryScores.personalityCommunication)%"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="compatibility-item">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-semibold">Care Needs & Skills</span>
                                            <span class="badge bg-success">#(match.categoryScores.careNeedsSkills)%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: #(match.categoryScores.careNeedsSkills)%"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="compatibility-item">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-semibold">Lifestyle & Interests</span>
                                            <span class="badge bg-info">#(match.categoryScores.lifestyleInterests)%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-info" style="width: #(match.categoryScores.lifestyleInterests)%"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="compatibility-item">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-semibold">Cultural & Location</span>
                                            <span class="badge bg-warning">#(match.categoryScores.culturalLanguageLocation)%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-warning" style="width: #(match.categoryScores.culturalLanguageLocation)%"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="compatibility-item">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-semibold">Logistics & Schedule</span>
                                            <span class="badge bg-secondary">#(match.categoryScores.logisticsSchedule)%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-secondary" style="width: #(match.categoryScores.logisticsSchedule)%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Client Responses -->
                            <h4 class="mb-3">
                                <i class="fas fa-comments text-info me-2"></i>
                                Client's Responses
                            </h4>

                            #if(clientResponses):
                                <div class="accordion" id="clientResponsesAccordion">
                                    #for(response in clientResponses):
                                        <div class="accordion-item mb-2">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed" type="button"
                                                        data-bs-toggle="collapse"
                                                        data-bs-target="#collapse#(response.id)"
                                                        aria-expanded="false">
                                                    #(response.questionText)
                                                </button>
                                            </h2>
                                            <div id="collapse#(response.id)"
                                                 class="accordion-collapse collapse"
                                                 data-bs-parent="#clientResponsesAccordion">
                                                <div class="accordion-body">
                                                    <ul class="list-group list-group-flush">
                                                        #for(option in response.selectedOptions):
                                                            <li class="list-group-item border-0 px-0">
                                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                                #(option)
                                                            </li>
                                                        #endfor
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    #endfor
                                </div>
                            #else:
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No responses available for this client.
                                </div>
                            #endif
                        </div>
                    </div>

                    <!-- Navigation Section -->
                    <div class="action-section">
                        <!-- Info Message -->
                        <div class="alert alert-info mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Information:</strong> As a caregiver, you can view potential client matches but cannot confirm or reject them.
                            Clients will review and decide on matches from their end.
                        </div>

                        <!-- Navigation Controls -->
                        <div class="secondary-actions">
                            <div class="row g-2 align-items-center">
                                <div class="col">
                                    #if(hasPrevious):
                                        <a href="/caregiver/#(caregiver.id)/matches/#(currentIndex - 1)" class="btn btn-outline-primary w-100">
                                            <i class="fas fa-chevron-left me-2"></i>
                                            Previous
                                        </a>
                                    #else:
                                        <button class="btn btn-outline-secondary w-100" disabled>
                                            <i class="fas fa-chevron-left me-2"></i>
                                            Previous
                                        </button>
                                    #endif
                                </div>

                                <div class="col-auto">
                                    <a href="/client/#(client.id)" class="btn btn-outline-info">
                                        <i class="fas fa-user"></i>
                                        <span class="d-none d-md-inline ms-2">Full Profile</span>
                                    </a>
                                </div>

                                <div class="col">
                                    #if(hasNext):
                                        <a href="/caregiver/#(caregiver.id)/matches/#(currentIndex + 1)" class="btn btn-outline-primary w-100">
                                            Next
                                            <i class="fas fa-chevron-right ms-2"></i>
                                        </a>
                                    #else:
                                        <button class="btn btn-outline-secondary w-100" disabled>
                                            Next
                                            <i class="fas fa-chevron-right ms-2"></i>
                                        </button>
                                    #endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Keyboard Navigation -->
        <script>
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft' && #(hasPrevious)) {
                window.location.href = '/caregiver/#(caregiver.id)/matches/#(currentIndex - 1)';
            } else if (e.key === 'ArrowRight' && #(hasNext)) {
                window.location.href = '/caregiver/#(caregiver.id)/matches/#(currentIndex + 1)';
            }
        });
        </script>
    #endexport

    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend

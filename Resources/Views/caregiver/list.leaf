#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport

    #export("content"):
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>All Caregivers</h1>
            <a href="/caregiver/register" class="btn btn-primary">Register New Caregiver</a>
        </div>

        #if(caregivers):
            <div class="card shadow">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                #for(caregiver in caregivers):
                                    <tr>
                                        <td>#(caregiver.name)</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="/caregiver/#(caregiver.id)" class="btn btn-outline-primary">View Profile</a>
                                                #if(isStaff):
                                                    <a href="/caregiver/#(caregiver.id)/matches" class="btn btn-outline-success">View Matches</a>
                                                #endif
                                            </div>
                                        </td>
                                    </tr>
                                #endfor
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        #else:
            <div class="alert alert-info">No caregivers registered yet.</div>
        #endif
    #endexport

    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend

#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport

    #export("content"):
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-4">
                    <h1 class="fw-bold mb-2">JavaScript Test</h1>
                    <p class="lead text-muted">Testing JavaScript functionality</p>
                </div>

                <div class="card shadow-lg border-0">
                    <div class="card-body p-4 p-md-5">
                        <div id="testContainer">
                            <div class="text-center py-5">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="text-muted">Loading test data...</p>
                            </div>
                        </div>
                        
                        <div id="results" class="mt-4"></div>
                    </div>
                </div>
            </div>
        </div>

        <script>
        console.log('JavaScript is running!');
        
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('DOM loaded');
            const testContainer = document.getElementById('testContainer');
            const resultsContainer = document.getElementById('results');
            
            try {
                console.log('Fetching questions...');
                const response = await fetch('/questions');
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const questions = await response.json();
                console.log('Questions loaded:', questions.length);
                
                testContainer.innerHTML = `
                    <div class="alert alert-success">
                        <h5>✅ JavaScript Test Successful!</h5>
                        <p>Successfully loaded ${questions.length} questions from the API.</p>
                    </div>
                `;
                
                resultsContainer.innerHTML = `
                    <h5>First Question:</h5>
                    <p><strong>Text:</strong> ${questions[0].questionText}</p>
                    <p><strong>Options:</strong> ${questions[0].options.join(', ')}</p>
                `;
                
            } catch (error) {
                console.error('Error:', error);
                testContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>❌ JavaScript Test Failed</h5>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        });
        </script>

    #endexport

    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend

#extend("base"):
#export("content"):
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="display-4 text-primary mb-3">
                        <i class="fas fa-language me-3"></i>Language Test
                    </h1>
                    <p class="lead text-muted">Test the Spanish translation functionality</p>
                </div>

                <!-- Language Selector -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-globe me-2"></i>Language Selection & Testing
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Switch Language:</h6>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-primary" onclick="setLanguageAndReload('en')">
                                        <i class="flag-icon flag-icon-us me-2"></i>English
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" onclick="setLanguageAndReload('es')">
                                        <i class="flag-icon flag-icon-es me-2"></i>Español
                                    </button>
                                </div>
                                <p class="text-muted mt-2 mb-0">
                                    <i class="fas fa-language me-1"></i>Current: <span id="currentLang">English</span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6>Compare Languages:</h6>
                                <button type="button" class="btn btn-info" onclick="showComparison()">
                                    <i class="fas fa-columns me-2"></i>Side-by-Side View
                                </button>
                                <p class="text-muted mt-2 mb-0">
                                    <i class="fas fa-eye me-1"></i>View both languages together
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Questions Display -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Sample Questions</h5>
                    </div>
                    <div class="card-body">
                        <div id="questionsContainer">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading questions...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentLanguage = 'en';

        async function setLanguageAndReload(languageCode) {
            try {
                // Update UI immediately
                updateLanguageButtons(languageCode);
                document.getElementById('currentLang').textContent = languageCode === 'en' ? 'English' : 'Español';

                // Show loading state
                document.getElementById('questionsContainer').innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading ${languageCode === 'en' ? 'English' : 'Spanish'} questions...</p>
                    </div>
                `;

                // Set language preference
                const response = await fetch(`/api/languages/set/${languageCode}`, {
                    method: 'POST',
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`Failed to set language: ${response.status}`);
                }

                currentLanguage = languageCode;

                // Load questions in the selected language
                await loadQuestions(languageCode);

            } catch (error) {
                console.error('Error switching language:', error);
                document.getElementById('questionsContainer').innerHTML = `
                    <div class="alert alert-danger">
                        Error switching language: ${error.message}
                    </div>
                `;
            }
        }

        function updateLanguageButtons(selectedLang) {
            // Update button states
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('btn-primary', 'btn-outline-primary');
                btn.classList.add('btn-outline-primary');
            });

            // Highlight selected button
            const selectedBtn = document.querySelector(`[onclick="setLanguageAndReload('${selectedLang}')"]`);
            if (selectedBtn) {
                selectedBtn.classList.remove('btn-outline-primary');
                selectedBtn.classList.add('btn-primary');
            }
        }

        async function loadQuestions(languageCode) {
            try {
                const response = await fetch(`/api/languages/${languageCode}/questions`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`Failed to load questions: ${response.status}`);
                }

                const questions = await response.json();

                const container = document.getElementById('questionsContainer');
                container.innerHTML = '';

                if (!questions || questions.length === 0) {
                    container.innerHTML = `
                        <div class="alert alert-warning">
                            No questions found for language: ${languageCode}
                        </div>
                    `;
                    return;
                }

                // Show first 5 questions as examples
                const sampleQuestions = questions.slice(0, 5);

                // Add language indicator
                const languageIndicator = document.createElement('div');
                languageIndicator.className = 'alert alert-info mb-3';
                languageIndicator.innerHTML = `
                    <i class="fas fa-language me-2"></i>
                    <strong>Displaying questions in: ${languageCode === 'en' ? 'English' : 'Español'}</strong>
                    <small class="d-block mt-1">Showing ${sampleQuestions.length} of ${questions.length} total questions</small>
                `;
                container.appendChild(languageIndicator);

                sampleQuestions.forEach((question, index) => {
                    const questionDiv = document.createElement('div');
                    questionDiv.className = 'mb-4 p-3 border rounded bg-light';
                    questionDiv.innerHTML = `
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-question-circle me-2"></i>
                            ${index + 1}. ${question.questionText}
                        </h6>
                        <div class="mt-3">
                            ${question.options.map((option, optIndex) => `
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="question${index}" value="${option}" id="q${index}_opt${optIndex}">
                                    <label class="form-check-label" for="q${index}_opt${optIndex}">
                                        ${option}
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-tag me-1"></i>Category: ${question.scoringCategory}
                        </small>
                    `;
                    container.appendChild(questionDiv);
                });

            } catch (error) {
                console.error('Error loading questions:', error);
                document.getElementById('questionsContainer').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Error loading questions:</strong> ${error.message}
                        <br><small>Please try refreshing the page or contact support.</small>
                    </div>
                `;
            }
        }

        // Load English questions on page load
        document.addEventListener('DOMContentLoaded', async function() {
            // Initialize with English
            updateLanguageButtons('en');

            // Load English questions
            await loadQuestions('en');
        });

        // Show side-by-side comparison
        async function showComparison() {
            try {
                document.getElementById('questionsContainer').innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading comparison view...</p>
                    </div>
                `;

                // Load both English and Spanish questions
                const [englishResponse, spanishResponse] = await Promise.all([
                    fetch('/api/languages/en/questions', { credentials: 'include' }),
                    fetch('/api/languages/es/questions', { credentials: 'include' })
                ]);

                const englishQuestions = await englishResponse.json();
                const spanishQuestions = await spanishResponse.json();

                const container = document.getElementById('questionsContainer');
                container.innerHTML = '';

                // Add comparison header
                const header = document.createElement('div');
                header.className = 'alert alert-success mb-4';
                header.innerHTML = `
                    <i class="fas fa-columns me-2"></i>
                    <strong>Side-by-Side Language Comparison</strong>
                    <small class="d-block mt-1">Comparing first 3 questions in English and Spanish</small>
                `;
                container.appendChild(header);

                // Show first 3 questions for comparison
                const sampleCount = 3;
                for (let i = 0; i < sampleCount && i < englishQuestions.length; i++) {
                    const englishQ = englishQuestions[i];
                    const spanishQ = spanishQuestions[i];

                    const comparisonDiv = document.createElement('div');
                    comparisonDiv.className = 'mb-4 border rounded';
                    comparisonDiv.innerHTML = `
                        <div class="row g-0">
                            <div class="col-md-6 p-3 border-end">
                                <h6 class="text-primary mb-2">
                                    <i class="flag-icon flag-icon-us me-2"></i>
                                    ${i + 1}. ${englishQ.questionText}
                                </h6>
                                <div class="mt-2">
                                    ${englishQ.options.map(option => `
                                        <div class="form-check mb-1">
                                            <input class="form-check-input" type="radio" disabled>
                                            <label class="form-check-label small">${option}</label>
                                        </div>
                                    `).join('')}
                                </div>
                                <small class="text-muted">${englishQ.scoringCategory}</small>
                            </div>
                            <div class="col-md-6 p-3 bg-light">
                                <h6 class="text-success mb-2">
                                    <i class="flag-icon flag-icon-es me-2"></i>
                                    ${i + 1}. ${spanishQ.questionText}
                                </h6>
                                <div class="mt-2">
                                    ${spanishQ.options.map(option => `
                                        <div class="form-check mb-1">
                                            <input class="form-check-input" type="radio" disabled>
                                            <label class="form-check-label small">${option}</label>
                                        </div>
                                    `).join('')}
                                </div>
                                <small class="text-muted">${spanishQ.scoringCategory}</small>
                            </div>
                        </div>
                    `;
                    container.appendChild(comparisonDiv);
                }

                // Add back button
                const backButton = document.createElement('div');
                backButton.className = 'text-center mt-4';
                backButton.innerHTML = `
                    <button class="btn btn-secondary" onclick="setLanguageAndReload('en')">
                        <i class="fas fa-arrow-left me-2"></i>Back to Single Language View
                    </button>
                `;
                container.appendChild(backButton);

            } catch (error) {
                console.error('Error loading comparison:', error);
                document.getElementById('questionsContainer').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Error loading comparison:</strong> ${error.message}
                    </div>
                `;
            }
        }
    </script>

    <style>
        .flag-icon {
            width: 16px;
            height: 12px;
            background-size: cover;
            display: inline-block;
        }

        .flag-icon-us {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxNiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjEyIiBmaWxsPSIjQjIyMjM0Ii8+CjxyZWN0IHk9IjEiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB5PSIzIiB3aWR0aD0iMTYiIGhlaWdodD0iMSIgZmlsbD0id2hpdGUiLz4KPHJlY3QgeT0iNSIgd2lkdGg9IjE2IiBoZWlnaHQ9IjEiIGZpbGw9IndoaXRlIi8+CjxyZWN0IHk9IjciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB5PSI5IiB3aWR0aD0iMTYiIGhlaWdodD0iMSIgZmlsbD0id2hpdGUiLz4KPHJlY3QgeT0iMTEiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSI3IiBmaWxsPSIjM0MzQjZFIi8+Cjwvc3ZnPgo=');
        }

        .flag-icon-es {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxNiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjEyIiBmaWxsPSIjRkZEQTAwIi8+CjxyZWN0IHdpZHRoPSIxNiIgaGVpZ2h0PSIzIiBmaWxsPSIjREEwMjBFIi8+CjxyZWN0IHk9IjkiIHdpZHRoPSIxNiIgaGVpZ2h0PSIzIiBmaWxsPSIjREEwMjBFIi8+Cjwvc3ZnPgo=');
        }
    </style>
#endexport
#endextend

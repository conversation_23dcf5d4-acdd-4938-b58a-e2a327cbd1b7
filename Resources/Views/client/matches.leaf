#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport
    
    #export("content"):
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Caregiver Matches for #(client.name)</h1>
            <a href="/client/#(client.id)" class="btn btn-secondary">Back to Profile</a>
        </div>
        
        #if(matches):
            <div class="row row-cols-1 row-cols-md-2 g-4">
                #for(match in matches):
                    <div class="col">
                        <div class="card h-100 shadow">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">#(match.caregiverName)</h5>
                                <span class="badge bg-primary rounded-pill fs-6">#(match.matchPercentage)% Match</span>
                            </div>
                            <div class="card-body">
                                <h6 class="card-subtitle mb-3 text-muted">Match Breakdown</h6>
                                
                                <div class="mb-3">
                                    <label class="form-label d-flex justify-content-between">
                                        <span>Personality & Communication</span>
                                        <span>#(match.categoryScores.personalityCommunication)%</span>
                                    </label>
                                    <div class="progress">
                                        <div class="progress-bar bg-primary" role="progressbar" style="width: #(match.categoryScores.personalityCommunication)%"
                                             aria-valuenow="#(match.categoryScores.personalityCommunication)" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label d-flex justify-content-between">
                                        <span>Care Needs & Skills</span>
                                        <span>#(match.categoryScores.careNeedsSkills)%</span>
                                    </label>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: #(match.categoryScores.careNeedsSkills)%"
                                             aria-valuenow="#(match.categoryScores.careNeedsSkills)" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label d-flex justify-content-between">
                                        <span>Lifestyle Interests</span>
                                        <span>#(match.categoryScores.lifestyleInterests)%</span>
                                    </label>
                                    <div class="progress">
                                        <div class="progress-bar bg-info" role="progressbar" style="width: #(match.categoryScores.lifestyleInterests)%"
                                             aria-valuenow="#(match.categoryScores.lifestyleInterests)" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label d-flex justify-content-between">
                                        <span>Cultural/Language/Location</span>
                                        <span>#(match.categoryScores.culturalLanguageLocation)%</span>
                                    </label>
                                    <div class="progress">
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: #(match.categoryScores.culturalLanguageLocation)%"
                                             aria-valuenow="#(match.categoryScores.culturalLanguageLocation)" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label d-flex justify-content-between">
                                        <span>Logistics & Schedule</span>
                                        <span>#(match.categoryScores.logisticsSchedule)%</span>
                                    </label>
                                    <div class="progress">
                                        <div class="progress-bar bg-danger" role="progressbar" style="width: #(match.categoryScores.logisticsSchedule)%"
                                             aria-valuenow="#(match.categoryScores.logisticsSchedule)" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a href="/caregiver/#(match.caregiverId)" class="btn btn-outline-primary">View Profile</a>
                            </div>
                        </div>
                    </div>
                #endfor
            </div>
        #else:
            <div class="alert alert-info">No matches found yet. Check back later!</div>
        #endif
    #endexport
    
    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend

#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport

    #export("content"):
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header with navigation -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="mb-1">Caregiver Match</h1>
                        <p class="text-muted mb-0">
                            <i class="fas fa-user me-1"></i>
                            Showing match #(currentIndex + 1) of #(totalMatches) for #(client.name)
                        </p>
                    </div>
                    <a href="/client/#(client.id)" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Profile
                    </a>
                </div>

                <!-- Match Banner -->
                <div class="match-banner mb-4">
                    <div class="match-banner-content">
                        <div class="match-banner-info">
                            <div class="match-banner-icon">
                                <i class="fas fa-user-nurse"></i>
                            </div>
                            <div class="match-banner-text">
                                <h2 class="match-banner-name">#(caregiver.name)</h2>
                                <p class="match-banner-subtitle">
                                    Potential Caregiver Match
                                    #if(confirmationStatus):
                                        <span class="badge ms-2 status-#(confirmationStatus)">#(confirmationStatus)</span>
                                    #endif
                                </p>
                            </div>
                        </div>
                        <div class="match-banner-percentage">
                            <span class="match-percentage-value">#(match.matchPercentage)%</span>
                            <span class="match-percentage-label">Match</span>
                        </div>
                    </div>
                </div>

                <!-- Match Card -->
                <div class="card shadow-lg border-0 mb-4">

                    <div class="card-body p-4">
                        <!-- Compatibility Scores -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-bar me-2 text-primary"></i>
                                Compatibility Breakdown
                            </h5>

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="compatibility-item">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-semibold">
                                                <i class="fas fa-comments text-primary me-2"></i>
                                                Personality & Communication
                                            </span>
                                            <span class="badge bg-primary">#(match.categoryScores.personalityCommunication)%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-primary" role="progressbar"
                                                 style="width: #(match.categoryScores.personalityCommunication)%"
                                                 aria-valuenow="#(match.categoryScores.personalityCommunication)"
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="compatibility-item">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-semibold">
                                                <i class="fas fa-user-md text-success me-2"></i>
                                                Care Needs & Skills
                                            </span>
                                            <span class="badge bg-success">#(match.categoryScores.careNeedsSkills)%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-success" role="progressbar"
                                                 style="width: #(match.categoryScores.careNeedsSkills)%"
                                                 aria-valuenow="#(match.categoryScores.careNeedsSkills)"
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="compatibility-item">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-semibold">
                                                <i class="fas fa-heart text-info me-2"></i>
                                                Lifestyle & Interests
                                            </span>
                                            <span class="badge bg-info">#(match.categoryScores.lifestyleInterests)%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-info" role="progressbar"
                                                 style="width: #(match.categoryScores.lifestyleInterests)%"
                                                 aria-valuenow="#(match.categoryScores.lifestyleInterests)"
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="compatibility-item">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-semibold">
                                                <i class="fas fa-globe text-warning me-2"></i>
                                                Cultural & Location
                                            </span>
                                            <span class="badge bg-warning">#(match.categoryScores.culturalLanguageLocation)%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-warning" role="progressbar"
                                                 style="width: #(match.categoryScores.culturalLanguageLocation)%"
                                                 aria-valuenow="#(match.categoryScores.culturalLanguageLocation)"
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="compatibility-item">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-semibold">
                                                <i class="fas fa-clock text-danger me-2"></i>
                                                Logistics & Schedule
                                            </span>
                                            <span class="badge bg-danger">#(match.categoryScores.logisticsSchedule)%</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-danger" role="progressbar"
                                                 style="width: #(match.categoryScores.logisticsSchedule)%"
                                                 aria-valuenow="#(match.categoryScores.logisticsSchedule)"
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Caregiver Responses -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-clipboard-list me-2 text-primary"></i>
                                Caregiver's Responses
                            </h5>

                            #if(caregiverResponses):
                                <div class="accordion" id="responsesAccordion">
                                    #for(response in caregiverResponses):
                                        <div class="accordion-item border-0 mb-2">
                                            <h6 class="accordion-header">
                                                <button class="accordion-button collapsed" type="button"
                                                        data-bs-toggle="collapse" data-bs-target="#response#(response.id)"
                                                        aria-expanded="false" aria-controls="response#(response.id)">
                                                    #(response.questionText)
                                                </button>
                                            </h6>
                                            <div id="response#(response.id)" class="accordion-collapse collapse"
                                                 data-bs-parent="#responsesAccordion">
                                                <div class="accordion-body">
                                                    <div class="d-flex flex-wrap gap-2">
                                                        #for(option in response.selectedOptions):
                                                            <span class="badge bg-light text-dark border">#(option)</span>
                                                        #endfor
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    #endfor
                                </div>
                            #else:
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No responses available for this caregiver.
                                </div>
                            #endif
                        </div>

                        <!-- Confirmation Status -->
                        #if(confirmationStatus):
                            <div class="alert alert-#if(confirmationStatus == "confirmed"):success#elseif(confirmationStatus == "rejected"):danger#else:warning#endif">
                                <i class="fas fa-#if(confirmationStatus == "confirmed"):check-circle#elseif(confirmationStatus == "rejected"):times-circle#else:clock#endif me-2"></i>
                                Status: <strong>#if(confirmationStatus == "confirmed"):Confirmed#elseif(confirmationStatus == "rejected"):Rejected#else:Pending#endif</strong>
                            </div>
                        #endif
                    </div>
                </div>

                <!-- Action Buttons - Cleaner Layout -->
                <div class="action-section">
                    <!-- Primary Actions - Confirm/Reject -->
                    #if(!confirmationStatus):
                        <div class="primary-actions mb-4">
                            <div class="row g-3">
                                <div class="col-sm-6">
                                    <button class="btn btn-success btn-lg w-100" onclick="confirmMatch('confirmed')">
                                        <i class="fas fa-check me-2"></i>
                                        Confirm Match
                                    </button>
                                </div>
                                <div class="col-sm-6">
                                    <button class="btn btn-danger btn-lg w-100" onclick="confirmMatch('rejected')">
                                        <i class="fas fa-times me-2"></i>
                                        Reject Match
                                    </button>
                                </div>
                            </div>
                        </div>
                    #else:
                        <!-- Already decided - show home button -->
                        <div class="primary-actions mb-4 text-center">
                            <a href="/" class="btn btn-primary btn-lg">
                                <i class="fas fa-home me-2"></i>
                                Back to Home
                            </a>
                        </div>
                    #endif

                    <!-- Secondary Actions -->
                    <div class="secondary-actions">
                        <div class="row g-2 align-items-center">
                            <div class="col">
                                #if(hasPrevious):
                                    <a href="/client/#(client.id)/matches/#(currentIndex - 1)" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-chevron-left me-2"></i>
                                        Previous
                                    </a>
                                #else:
                                    <button class="btn btn-outline-secondary w-100" disabled>
                                        <i class="fas fa-chevron-left me-2"></i>
                                        Previous
                                    </button>
                                #endif
                            </div>

                            <div class="col-auto">
                                <a href="/caregiver/#(caregiver.id)" class="btn btn-outline-info">
                                    <i class="fas fa-user"></i>
                                    <span class="d-none d-md-inline ms-2">Full Profile</span>
                                </a>
                            </div>

                            <div class="col">
                                #if(hasNext):
                                    <a href="/client/#(client.id)/matches/#(currentIndex + 1)" class="btn btn-outline-primary w-100">
                                        Next
                                        <i class="fas fa-chevron-right ms-2"></i>
                                    </a>
                                #else:
                                    <button class="btn btn-outline-secondary w-100" disabled>
                                        Next
                                        <i class="fas fa-chevron-right ms-2"></i>
                                    </button>
                                #endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Confirmation Modal -->
        <div class="modal fade" id="confirmationModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirm Your Decision</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p id="confirmationMessage"></p>
                        <div class="mb-3">
                            <label for="confirmationNotes" class="form-label">Notes (optional)</label>
                            <textarea class="form-control" id="confirmationNotes" rows="3"
                                      placeholder="Add any additional notes about your decision..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn" id="confirmButton" onclick="submitConfirmation()">Confirm</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
        let currentAction = '';

        function confirmMatch(action) {
            currentAction = action;
            const modal = new bootstrap.Modal(document.getElementById('confirmationModal'));
            const message = document.getElementById('confirmationMessage');
            const confirmButton = document.getElementById('confirmButton');

            if (action === 'confirmed') {
                message.textContent = 'Are you sure you want to confirm this caregiver match? This will indicate your interest in working with this caregiver.';
                confirmButton.textContent = 'Confirm Match';
                confirmButton.className = 'btn btn-success';
            } else {
                message.textContent = 'Are you sure you want to reject this caregiver match? This will remove them from your potential matches.';
                confirmButton.textContent = 'Reject Match';
                confirmButton.className = 'btn btn-danger';
            }

            modal.show();
        }

        function submitConfirmation() {
            const notes = document.getElementById('confirmationNotes').value;
            const clientId = '#(client.id)';
            const caregiverId = '#(caregiver.id)';

            // Show loading state
            const confirmButton = document.getElementById('confirmButton');
            const originalText = confirmButton.textContent;
            confirmButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Saving...';
            confirmButton.disabled = true;

            // For rejections, use form submission to allow server redirect
            // For confirmations, use fetch to show success message before redirect
            if (currentAction === 'rejected') {
                // Create a form and submit it to let server handle redirect
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/client/${clientId}/matches/${caregiverId}/confirm`;

                // Add status field
                const statusField = document.createElement('input');
                statusField.type = 'hidden';
                statusField.name = 'status';
                statusField.value = currentAction;
                form.appendChild(statusField);

                // Add notes field if provided
                if (notes) {
                    const notesField = document.createElement('input');
                    notesField.type = 'hidden';
                    notesField.name = 'notes';
                    notesField.value = notes;
                    form.appendChild(notesField);
                }

                // Add CSRF token if needed (Vapor doesn't require it by default)
                document.body.appendChild(form);
                form.submit();
            } else {
                // For confirmations, use fetch and redirect to home
                fetch(`/client/${clientId}/matches/${caregiverId}/confirm`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        status: currentAction,
                        notes: notes || null
                    })
                })
                .then(response => {
                    if (response.ok) {
                        // Show success message and redirect to home
                        confirmButton.innerHTML = '<i class="fas fa-check me-2"></i>Success!';
                        confirmButton.className = 'btn btn-success';

                        setTimeout(() => {
                            window.location.href = '/';
                        }, 1000);
                    } else {
                        throw new Error('Failed to save confirmation');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    confirmButton.innerHTML = originalText;
                    confirmButton.disabled = false;
                    alert('Failed to save your decision. Please try again.');
                });
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft' && #(hasPrevious)) {
                window.location.href = '/client/#(client.id)/matches/#(currentIndex - 1)';
            } else if (e.key === 'ArrowRight' && #(hasNext)) {
                window.location.href = '/client/#(client.id)/matches/#(currentIndex + 1)';
            }
        });
        </script>
    #endexport

    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend

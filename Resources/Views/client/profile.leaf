#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport

    #export("content"):
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Client Profile</h1>
            <div class="btn-group">
                #if(isStaff):
                    <a href="/client/#(client.id)/matches" class="btn btn-primary">View Matches</a>
                #endif
                <button type="button" class="btn btn-danger" onclick="deleteClient('#(client.id)')">
                    <i class="fas fa-trash me-2"></i>Delete Profile
                </button>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-body">
                <h2 class="card-title mb-4">#(client.name)</h2>

                <h4 class="mb-3">Your Responses</h4>

                #if(responses):
                    #for(response in responses):
                        <div class="card mb-3">
                            <div class="card-header">
                                <strong>#(response.questionText)</strong>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    #for(option in response.selectedOptions):
                                        <li class="list-group-item">#(option)</li>
                                    #endfor
                                </ul>
                            </div>
                        </div>
                    #endfor
                #else:
                    <div class="alert alert-info">No responses found.</div>
                #endif
            </div>
        </div>
    #endexport

    #export("footer"):
        #extend("shared/footer")

        <script>
        function deleteClient(clientId) {
            if (confirm('Are you sure you want to delete this client profile? This action cannot be undone.')) {
                fetch(`/clients/${clientId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        alert('Client profile deleted successfully');
                        window.location.href = '/clients';
                    } else {
                        throw new Error('Failed to delete client');
                    }
                })
                .catch(error => {
                    console.error('Error deleting client:', error);
                    alert('Failed to delete client profile. Please try again.');
                });
            }
        }
        </script>
    #endexport
#endextend

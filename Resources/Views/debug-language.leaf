<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Debug - MatchIQ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Language Debug Test</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Language Selection</h3>
                <select id="languageSelect" class="form-select">
                    <option value="en">English</option>
                    <option value="es">Español</option>
                </select>
                <button id="loadQuestions" class="btn btn-primary mt-2">Load Questions</button>
            </div>
            
            <div class="col-md-6">
                <h3>API Test Results</h3>
                <div id="results" class="border p-3" style="min-height: 200px;">
                    Click "Load Questions" to test the API
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>Debug Log</h3>
                <div id="debugLog" class="border p-3 bg-light" style="min-height: 150px; font-family: monospace;">
                    Debug information will appear here...
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}<br>`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        document.getElementById('loadQuestions').addEventListener('click', async function() {
            const languageSelect = document.getElementById('languageSelect');
            const selectedLanguage = languageSelect.value;
            const resultsDiv = document.getElementById('results');
            
            log(`Starting test with language: ${selectedLanguage}`);
            
            try {
                // Test the language-specific API
                const apiUrl = `/api/languages/${selectedLanguage}/questions`;
                log(`Making request to: ${apiUrl}`);
                
                resultsDiv.innerHTML = '<div class="spinner-border" role="status"></div> Loading...';
                
                const response = await fetch(apiUrl, {
                    credentials: 'include'
                });
                
                log(`Response status: ${response.status}`);
                log(`Response headers: ${JSON.stringify([...response.headers.entries()])}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const questions = await response.json();
                log(`Received ${questions.length} questions`);
                
                // Display first question as example
                if (questions.length > 0) {
                    const firstQuestion = questions[0];
                    resultsDiv.innerHTML = `
                        <h5>First Question (${selectedLanguage}):</h5>
                        <p><strong>Text:</strong> ${firstQuestion.questionText}</p>
                        <p><strong>Options:</strong> ${firstQuestion.options.join(', ')}</p>
                        <p><strong>Total Questions:</strong> ${questions.length}</p>
                    `;
                    log(`First question text: "${firstQuestion.questionText}"`);
                } else {
                    resultsDiv.innerHTML = '<p class="text-warning">No questions received</p>';
                    log('No questions in response');
                }
                
            } catch (error) {
                log(`ERROR: ${error.message}`);
                resultsDiv.innerHTML = `<p class="text-danger">Error: ${error.message}</p>`;
            }
        });

        // Initialize
        log('Debug page loaded');
        log(`Current URL: ${window.location.href}`);
    </script>
</body>
</html>

#extend("base"):
    #export("title", title)
    #export("content"):
        <div class="container-fluid">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-users text-primary me-2"></i>Confirmed Client Matches
                    </h1>
                    <p class="text-muted mb-0">View all confirmed client-caregiver matches</p>
                </div>
                <div class="btn-group" role="group">
                    <a href="/staff/matches" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>All Matches
                    </a>
                    <a href="/staff/matches/caregivers" class="btn btn-outline-primary">
                        <i class="fas fa-user-nurse me-1"></i>Caregiver Matches
                    </a>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>#(totalClients)</h4>
                                    <p class="mb-0">Clients with Confirmed Matches</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>#(totalMatches)</h4>
                                    <p class="mb-0">Total Confirmed Matches</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-heart fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>
                                        #if(totalClients > 0):
                                            #(totalMatches / totalClients)
                                        #else:
                                            0
                                        #endif
                                    </h4>
                                    <p class="mb-0">Avg Matches per Client</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Matches Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Confirmed Client Matches
                    </h5>
                </div>
                <div class="card-body">
                    #if(clientMatches):
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Client</th>
                                        <th>Contact</th>
                                        <th>Location</th>
                                        <th>Language</th>
                                        <th>Confirmed Matches</th>
                                        <th>Best Match</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    #for(clientMatch in clientMatches):
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <strong>#(clientMatch.client.name)</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    #if(clientMatch.client.email):
                                                        <div class="text-sm">
                                                            <i class="fas fa-envelope text-muted me-1"></i>
                                                            <a href="mailto:#(clientMatch.client.email)">#(clientMatch.client.email)</a>
                                                        </div>
                                                    #endif
                                                    #if(clientMatch.client.phone):
                                                        <div class="text-sm">
                                                            <i class="fas fa-phone text-muted me-1"></i>
                                                            <a href="tel:#(clientMatch.client.phone)">#(clientMatch.client.phone)</a>
                                                        </div>
                                                    #endif
                                                </div>
                                            </td>
                                            <td>
                                                #if(clientMatch.client.location):
                                                    <span class="badge bg-light text-dark">
                                                        <i class="fas fa-map-marker-alt me-1"></i>#(clientMatch.client.location)
                                                    </span>
                                                #else:
                                                    <span class="text-muted">Not specified</span>
                                                #endif
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-language me-1"></i>#(clientMatch.client.language)
                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-success me-2">#(clientMatch.totalConfirmed)</span>
                                                    <small class="text-muted">confirmed</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-2" style="width: 60px; height: 8px;">
                                                        <div class="progress-bar 
                                                            #if(clientMatch.bestMatchPercentage >= 80):
                                                                bg-success
                                                            #elseif(clientMatch.bestMatchPercentage >= 60):
                                                                bg-warning
                                                            #else:
                                                                bg-danger
                                                            #endif
                                                        " style="width: #(clientMatch.bestMatchPercentage)%"></div>
                                                    </div>
                                                    <small class="text-muted">#(clientMatch.bestMatchPercentage)%</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="/client/#(clientMatch.client.id)" class="btn btn-outline-primary" title="View Client Profile">
                                                        <i class="fas fa-user"></i>
                                                    </a>
                                                    <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#clientMatchModal#(clientMatch.client.id)" title="View Confirmed Matches">
                                                        <i class="fas fa-heart"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    #endfor
                                </tbody>
                            </table>
                        </div>
                    #else:
                        <div class="text-center py-5">
                            <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Confirmed Matches</h5>
                            <p class="text-muted">No client matches have been confirmed yet.</p>
                            <div class="mt-3">
                                <a href="/staff/matches" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>View All Matches
                                </a>
                                <a href="/staff/clients" class="btn btn-success">
                                    <i class="fas fa-users me-1"></i>View Clients
                                </a>
                            </div>
                        </div>
                    #endif
                </div>
            </div>
        </div>

        <!-- Modals for viewing confirmed matches -->
        #for(clientMatch in clientMatches):
            <div class="modal fade" id="clientMatchModal#(clientMatch.client.id)" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-heart text-success me-2"></i>
                                Confirmed Matches for #(clientMatch.client.name)
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                #for(caregiverInfo in clientMatch.confirmedCaregivers):
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0">#(caregiverInfo.caregiver.name)</h6>
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge bg-success me-2">#(caregiverInfo.matchPercentage)%</span>
                                                        #if(caregiverInfo.staffValidated):
                                                            <span class="badge bg-primary" title="Staff Validated">
                                                                <i class="fas fa-check-circle"></i>
                                                            </span>
                                                        #else:
                                                            <span class="badge bg-warning" title="Pending Staff Validation">
                                                                <i class="fas fa-clock"></i>
                                                            </span>
                                                        #endif
                                                    </div>
                                                </div>
                                                <div class="text-sm text-muted mb-2">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    Confirmed: #date(caregiverInfo.confirmedAt, "MMM d, yyyy")
                                                </div>
                                                #if(caregiverInfo.staffValidated):
                                                    <div class="text-sm text-success mb-2">
                                                        <i class="fas fa-user-check me-1"></i>
                                                        Staff Validated
                                                        #if(caregiverInfo.validatedByStaffEmail):
                                                            by #(caregiverInfo.validatedByStaffEmail)
                                                        #endif
                                                        #if(caregiverInfo.staffValidatedAt):
                                                            on #date(caregiverInfo.staffValidatedAt, "MMM d, yyyy")
                                                        #endif
                                                    </div>
                                                #endif
                                                #if(caregiverInfo.notes):
                                                    <div class="text-sm mb-2">
                                                        <strong>Client Notes:</strong> #(caregiverInfo.notes)
                                                    </div>
                                                #endif
                                                #if(caregiverInfo.staffValidationNotes):
                                                    <div class="text-sm mb-2">
                                                        <strong>Staff Notes:</strong> #(caregiverInfo.staffValidationNotes)
                                                    </div>
                                                #endif
                                                <div class="mt-2 d-flex gap-2">
                                                    <a href="/caregiver/#(caregiverInfo.caregiver.id)" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-user-nurse me-1"></i>View Profile
                                                    </a>
                                                    #if(caregiverInfo.staffValidated):
                                                        <button class="btn btn-sm btn-outline-warning" onclick="invalidateMatch('#(caregiverInfo.confirmationId)')">
                                                            <i class="fas fa-times me-1"></i>Invalidate
                                                        </button>
                                                    #else:
                                                        <button class="btn btn-sm btn-outline-success" onclick="validateMatch('#(caregiverInfo.confirmationId)')">
                                                            <i class="fas fa-check me-1"></i>Validate
                                                        </button>
                                                    #endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                #endfor
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        #endfor

        <!-- Validation Modal -->
        <div class="modal fade" id="validationModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="validationModalTitle">Validate Match</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="validationForm">
                            <div class="mb-3">
                                <label for="validationNotes" class="form-label">Notes (Optional)</label>
                                <textarea class="form-control" id="validationNotes" rows="3" placeholder="Add any notes about this validation..."></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="confirmValidationBtn">Confirm</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let currentConfirmationId = null;
            let currentAction = null;

            function validateMatch(confirmationId) {
                currentConfirmationId = confirmationId;
                currentAction = 'validate';
                document.getElementById('validationModalTitle').textContent = 'Validate Match';
                document.getElementById('confirmValidationBtn').textContent = 'Validate';
                document.getElementById('confirmValidationBtn').className = 'btn btn-success';
                document.getElementById('validationNotes').value = '';
                new bootstrap.Modal(document.getElementById('validationModal')).show();
            }

            function invalidateMatch(confirmationId) {
                currentConfirmationId = confirmationId;
                currentAction = 'invalidate';
                document.getElementById('validationModalTitle').textContent = 'Invalidate Match';
                document.getElementById('confirmValidationBtn').textContent = 'Invalidate';
                document.getElementById('confirmValidationBtn').className = 'btn btn-warning';
                document.getElementById('validationNotes').value = '';
                new bootstrap.Modal(document.getElementById('validationModal')).show();
            }

            document.getElementById('confirmValidationBtn').addEventListener('click', function() {
                if (!currentConfirmationId || !currentAction) return;

                const notes = document.getElementById('validationNotes').value;
                const url = `/staff/matches/${currentAction}/${currentConfirmationId}`;

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ notes: notes })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Close modal and reload page to show updated status
                        bootstrap.Modal.getInstance(document.getElementById('validationModal')).hide();
                        location.reload();
                    } else {
                        alert('Error: ' + (data.message || 'Unknown error occurred'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while processing the request');
                });
            });
        </script>
    #endexport
#endextend

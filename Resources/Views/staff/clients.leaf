#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport
    
    #export("content"):
        <div class="container-fluid">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-users me-2 text-primary"></i>All Clients
                    </h1>
                    <p class="text-muted mb-0">Manage and view all registered clients</p>
                </div>
                <div>
                    <a href="/staff/dashboard" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                    <a href="/register" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Add New Client
                    </a>
                </div>
            </div>

            <!-- Stats Card -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">#(clients.count)</h4>
                                    <p class="mb-0">Total Clients</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Clients Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Client Directory
                    </h5>
                </div>
                <div class="card-body">
                    #if(clients):
                        <div class="table-responsive">
                            <table class="table table-hover" id="clientsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th class="sortable" data-column="name">
                                            Name <i class="fas fa-sort text-muted"></i>
                                        </th>
                                        <th class="sortable" data-column="email">
                                            Email <i class="fas fa-sort text-muted"></i>
                                        </th>
                                        <th class="sortable" data-column="phone">
                                            Phone <i class="fas fa-sort text-muted"></i>
                                        </th>
                                        <th class="sortable" data-column="location">
                                            Location <i class="fas fa-sort text-muted"></i>
                                        </th>
                                        <th class="sortable" data-column="language">
                                            Language <i class="fas fa-sort text-muted"></i>
                                        </th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    #for(client in clients):
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <strong>#(client.name)</strong>
                                                </div>
                                            </td>
                                            <td>
                                                #if(client.email):
                                                    <a href="mailto:#(client.email)" class="text-decoration-none">
                                                        #(client.email)
                                                    </a>
                                                #else:
                                                    <span class="text-muted">—</span>
                                                #endif
                                            </td>
                                            <td>
                                                #if(client.phone):
                                                    <a href="tel:#(client.phone)" class="text-decoration-none">
                                                        #(client.phone)
                                                    </a>
                                                #else:
                                                    <span class="text-muted">—</span>
                                                #endif
                                            </td>
                                            <td>
                                                <i class="fas fa-map-marker-alt me-1 text-muted"></i>
                                                #(client.location)
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    #if(client.language == "en"):
                                                        English
                                                    #elseif(client.language == "es"):
                                                        Spanish
                                                    #else:
                                                        #(client.language)
                                                    #endif
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="/client/#(client.id)" class="btn btn-outline-primary" title="View Profile">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="/client/#(client.id)/matches" class="btn btn-outline-success" title="View Matches">
                                                        <i class="fas fa-heart"></i>
                                                    </a>
                                                    <button class="btn btn-outline-danger" title="Delete Client" onclick="deleteClient('#(client.id)', '#(client.name)')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    #endfor
                                </tbody>
                            </table>
                        </div>
                    #else:
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Clients Found</h5>
                            <p class="text-muted">No clients have been registered yet.</p>
                            <a href="/register" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Register First Client
                            </a>
                        </div>
                    #endif
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deleteModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirm Deletion</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete client <strong id="clientName"></strong>?</p>
                        <p class="text-danger"><small>This action cannot be undone.</small></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmDelete">Delete Client</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let clientToDelete = null;

            function deleteClient(clientId, clientName) {
                clientToDelete = clientId;
                document.getElementById('clientName').textContent = clientName;
                new bootstrap.Modal(document.getElementById('deleteModal')).show();
            }

            document.getElementById('confirmDelete').addEventListener('click', function() {
                if (clientToDelete) {
                    // TODO: Implement delete functionality
                    alert('Delete functionality will be implemented soon.');
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                }
            });
        </script>

        <script>
            // Table sorting functionality
            document.addEventListener('DOMContentLoaded', function() {
                const table = document.getElementById('clientsTable');
                if (!table) return;

                const headers = table.querySelectorAll('th.sortable');
                let currentSort = { column: null, direction: 'asc' };

                headers.forEach(header => {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', function() {
                        const column = this.dataset.column;
                        const direction = currentSort.column === column && currentSort.direction === 'asc' ? 'desc' : 'asc';

                        sortTable(table, column, direction);
                        updateSortIcons(headers, column, direction);

                        currentSort = { column, direction };
                    });
                });
            });

            function sortTable(table, column, direction) {
                const tbody = table.querySelector('tbody');
                const rows = Array.from(tbody.querySelectorAll('tr'));

                rows.sort((a, b) => {
                    let aVal = getCellValue(a, column);
                    let bVal = getCellValue(b, column);

                    // Convert to lowercase for case-insensitive sorting
                    if (typeof aVal === 'string') aVal = aVal.toLowerCase();
                    if (typeof bVal === 'string') bVal = bVal.toLowerCase();

                    if (direction === 'asc') {
                        return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
                    } else {
                        return aVal < bVal ? 1 : aVal > bVal ? -1 : 0;
                    }
                });

                // Re-append sorted rows
                rows.forEach(row => tbody.appendChild(row));
            }

            function getCellValue(row, column) {
                const columnIndex = getColumnIndex(column);
                const cell = row.cells[columnIndex];

                if (column === 'name') {
                    return cell.querySelector('strong').textContent.trim();
                } else if (column === 'email') {
                    const emailLink = cell.querySelector('a');
                    return emailLink ? emailLink.textContent.trim() : '';
                } else if (column === 'phone') {
                    const phoneLink = cell.querySelector('a');
                    return phoneLink ? phoneLink.textContent.trim() : '';
                } else if (column === 'location') {
                    return cell.textContent.replace(/.*\s/, '').trim(); // Remove icon
                } else if (column === 'language') {
                    return cell.querySelector('.badge').textContent.trim();
                }

                return cell.textContent.trim();
            }

            function getColumnIndex(column) {
                const columns = ['name', 'email', 'phone', 'location', 'language'];
                return columns.indexOf(column);
            }

            function updateSortIcons(headers, activeColumn, direction) {
                headers.forEach(header => {
                    const icon = header.querySelector('i');
                    const column = header.dataset.column;

                    if (column === activeColumn) {
                        icon.className = direction === 'asc' ? 'fas fa-sort-up text-primary' : 'fas fa-sort-down text-primary';
                    } else {
                        icon.className = 'fas fa-sort text-muted';
                    }
                });
            }
        </script>

        <style>
            .avatar-sm {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }

            .sortable {
                user-select: none;
                transition: background-color 0.2s;
            }

            .sortable:hover {
                background-color: rgba(0, 0, 0, 0.05);
            }
        </style>
    #endexport
    
    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend

#extend("base"):
    #export("title", title)
    #export("content"):
        <div class="container-fluid">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-nurse text-primary me-2"></i>Confirmed Caregiver Matches
                    </h1>
                    <p class="text-muted mb-0">View all confirmed caregiver-client matches</p>
                </div>
                <div class="btn-group" role="group">
                    <a href="/staff/matches" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>All Matches
                    </a>
                    <a href="/staff/matches/clients" class="btn btn-outline-primary">
                        <i class="fas fa-users me-1"></i>Client Matches
                    </a>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>#(totalCaregivers)</h4>
                                    <p class="mb-0">Caregivers with Confirmed Matches</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-nurse fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>#(totalMatches)</h4>
                                    <p class="mb-0">Total Confirmed Matches</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-heart fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>
                                        #if(totalCaregivers > 0):
                                            #(totalMatches / totalCaregivers)
                                        #else:
                                            0
                                        #endif
                                    </h4>
                                    <p class="mb-0">Avg Matches per Caregiver</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Caregiver Matches Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Confirmed Caregiver Matches
                    </h5>
                </div>
                <div class="card-body">
                    #if(caregiverMatches):
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Caregiver</th>
                                        <th>Contact</th>
                                        <th>Location</th>
                                        <th>Language</th>
                                        <th>Confirmed Matches</th>
                                        <th>Best Match</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    #for(caregiverMatch in caregiverMatches):
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="fas fa-user-nurse"></i>
                                                    </div>
                                                    <div>
                                                        <strong>#(caregiverMatch.caregiver.name)</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    #if(caregiverMatch.caregiver.email):
                                                        <div class="text-sm">
                                                            <i class="fas fa-envelope text-muted me-1"></i>
                                                            <a href="mailto:#(caregiverMatch.caregiver.email)">#(caregiverMatch.caregiver.email)</a>
                                                        </div>
                                                    #endif
                                                    #if(caregiverMatch.caregiver.phone):
                                                        <div class="text-sm">
                                                            <i class="fas fa-phone text-muted me-1"></i>
                                                            <a href="tel:#(caregiverMatch.caregiver.phone)">#(caregiverMatch.caregiver.phone)</a>
                                                        </div>
                                                    #endif
                                                </div>
                                            </td>
                                            <td>
                                                #if(caregiverMatch.caregiver.location):
                                                    <span class="badge bg-light text-dark">
                                                        <i class="fas fa-map-marker-alt me-1"></i>#(caregiverMatch.caregiver.location)
                                                    </span>
                                                #else:
                                                    <span class="text-muted">Not specified</span>
                                                #endif
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-language me-1"></i>#(caregiverMatch.caregiver.language)
                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-success me-2">#(caregiverMatch.totalConfirmed)</span>
                                                    <small class="text-muted">confirmed</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-2" style="width: 60px; height: 8px;">
                                                        <div class="progress-bar 
                                                            #if(caregiverMatch.bestMatchPercentage >= 80):
                                                                bg-success
                                                            #elseif(caregiverMatch.bestMatchPercentage >= 60):
                                                                bg-warning
                                                            #else:
                                                                bg-danger
                                                            #endif
                                                        " style="width: #(caregiverMatch.bestMatchPercentage)%"></div>
                                                    </div>
                                                    <small class="text-muted">#(caregiverMatch.bestMatchPercentage)%</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="/caregiver/#(caregiverMatch.caregiver.id)" class="btn btn-outline-primary" title="View Caregiver Profile">
                                                        <i class="fas fa-user-nurse"></i>
                                                    </a>
                                                    <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#caregiverMatchModal#(caregiverMatch.caregiver.id)" title="View Confirmed Matches">
                                                        Validate
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    #endfor
                                </tbody>
                            </table>
                        </div>
                    #else:
                        <div class="text-center py-5">
                            <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Confirmed Matches</h5>
                            <p class="text-muted">No caregiver matches have been confirmed yet.</p>
                            <div class="mt-3">
                                <a href="/staff/matches" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>View All Matches
                                </a>
                                <a href="/staff/caregivers" class="btn btn-success">
                                    <i class="fas fa-user-nurse me-1"></i>View Caregivers
                                </a>
                            </div>
                        </div>
                    #endif
                </div>
            </div>
        </div>

        <!-- Modals for viewing confirmed matches -->
        #for(caregiverMatch in caregiverMatches):
            <div class="modal fade" id="caregiverMatchModal#(caregiverMatch.caregiver.id)" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-heart text-success me-2"></i>
                                Confirmed Matches for #(caregiverMatch.caregiver.name)
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                #for(clientInfo in caregiverMatch.confirmedClients):
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0">#(clientInfo.client.name)</h6>
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge bg-success me-2">#(clientInfo.matchPercentage)%</span>
                                                        #if(clientInfo.staffValidated):
                                                            <span class="badge bg-primary" title="Staff Validated">
                                                                <i class="fas fa-check-circle"></i>
                                                            </span>
                                                        #else:
                                                            <span class="badge bg-warning" title="Pending Staff Validation">
                                                                <i class="fas fa-clock"></i>
                                                            </span>
                                                        #endif
                                                    </div>
                                                </div>
                                                <div class="text-sm text-muted mb-2">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    Confirmed: #date(clientInfo.confirmedAt, "MMM d, yyyy")
                                                </div>
                                                #if(clientInfo.staffValidated):
                                                    <div class="text-sm text-success mb-2">
                                                        <i class="fas fa-user-check me-1"></i>
                                                        Staff Validated
                                                        #if(clientInfo.validatedByStaffEmail):
                                                            by #(clientInfo.validatedByStaffEmail)
                                                        #endif
                                                        #if(clientInfo.staffValidatedAt):
                                                            on #date(clientInfo.staffValidatedAt, "MMM d, yyyy")
                                                        #endif
                                                    </div>
                                                #endif
                                                #if(clientInfo.notes):
                                                    <div class="text-sm mb-2">
                                                        <strong>Client Notes:</strong> #(clientInfo.notes)
                                                    </div>
                                                #endif
                                                #if(clientInfo.staffValidationNotes):
                                                    <div class="text-sm mb-2">
                                                        <strong>Staff Notes:</strong> #(clientInfo.staffValidationNotes)
                                                    </div>
                                                #endif
                                                <div class="mt-2 d-flex gap-2">
                                                    <a href="/client/#(clientInfo.client.id)" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-user me-1"></i>View Profile
                                                    </a>
                                                    #if(clientInfo.staffValidated):
                                                        <button class="btn btn-sm btn-outline-warning" onclick="invalidateMatch('#(clientInfo.confirmationId)')">
                                                            <i class="fas fa-times me-1"></i>Invalidate
                                                        </button>
                                                    #else:
                                                        <button class="btn btn-sm btn-outline-success" onclick="validateMatch('#(clientInfo.confirmationId)')">
                                                            <i class="fas fa-check me-1"></i>Validate
                                                        </button>
                                                    #endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                #endfor
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        #endfor

        <!-- Validation Modal -->
        <div class="modal fade" id="validationModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="validationModalTitle">Validate Match</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="validationForm">
                            <div class="mb-3">
                                <label for="validationNotes" class="form-label">Notes (Optional)</label>
                                <textarea class="form-control" id="validationNotes" rows="3" placeholder="Add any notes about this validation..."></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="confirmValidationBtn">Confirm</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let currentConfirmationId = null;
            let currentAction = null;

            function validateMatch(confirmationId) {
                currentConfirmationId = confirmationId;
                currentAction = 'validate';
                document.getElementById('validationModalTitle').textContent = 'Validate Match';
                document.getElementById('confirmValidationBtn').textContent = 'Validate';
                document.getElementById('confirmValidationBtn').className = 'btn btn-success';
                document.getElementById('validationNotes').value = '';
                new bootstrap.Modal(document.getElementById('validationModal')).show();
            }

            function invalidateMatch(confirmationId) {
                currentConfirmationId = confirmationId;
                currentAction = 'invalidate';
                document.getElementById('validationModalTitle').textContent = 'Invalidate Match';
                document.getElementById('confirmValidationBtn').textContent = 'Invalidate';
                document.getElementById('confirmValidationBtn').className = 'btn btn-warning';
                document.getElementById('validationNotes').value = '';
                new bootstrap.Modal(document.getElementById('validationModal')).show();
            }

            document.getElementById('confirmValidationBtn').addEventListener('click', function() {
                if (!currentConfirmationId || !currentAction) return;

                const notes = document.getElementById('validationNotes').value;
                const url = `/staff/matches/${currentAction}/${currentConfirmationId}`;

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ notes: notes })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Close modal and reload page to show updated status
                        bootstrap.Modal.getInstance(document.getElementById('validationModal')).hide();
                        location.reload();
                    } else {
                        alert('Error: ' + (data.message || 'Unknown error occurred'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while processing the request');
                });
            });
        </script>
    #endexport
#endextend

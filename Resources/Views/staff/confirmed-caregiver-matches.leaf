#extend("base"):
    #export("title", #(title))
    #export("content"):
        <div class="container-fluid">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-nurse text-primary me-2"></i>Confirmed Caregiver Matches
                    </h1>
                    <p class="text-muted mb-0">View all confirmed caregiver-client matches</p>
                </div>
                <div class="btn-group" role="group">
                    <a href="/staff/matches" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>All Matches
                    </a>
                    <a href="/staff/matches/clients" class="btn btn-outline-primary">
                        <i class="fas fa-users me-1"></i>Client Matches
                    </a>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>#(totalCaregivers)</h4>
                                    <p class="mb-0">Caregivers with Confirmed Matches</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-nurse fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>#(totalMatches)</h4>
                                    <p class="mb-0">Total Confirmed Matches</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-heart fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>
                                        #if(totalCaregivers > 0):
                                            #(totalMatches / totalCaregivers)
                                        #else:
                                            0
                                        #endif
                                    </h4>
                                    <p class="mb-0">Avg Matches per Caregiver</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Caregiver Matches Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Confirmed Caregiver Matches
                    </h5>
                </div>
                <div class="card-body">
                    #if(caregiverMatches):
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Caregiver</th>
                                        <th>Contact</th>
                                        <th>Location</th>
                                        <th>Language</th>
                                        <th>Confirmed Matches</th>
                                        <th>Best Match</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    #for(caregiverMatch in caregiverMatches):
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="fas fa-user-nurse"></i>
                                                    </div>
                                                    <div>
                                                        <strong>#(caregiverMatch.caregiver.name)</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    #if(caregiverMatch.caregiver.email):
                                                        <div class="text-sm">
                                                            <i class="fas fa-envelope text-muted me-1"></i>
                                                            <a href="mailto:#(caregiverMatch.caregiver.email)">#(caregiverMatch.caregiver.email)</a>
                                                        </div>
                                                    #endif
                                                    #if(caregiverMatch.caregiver.phone):
                                                        <div class="text-sm">
                                                            <i class="fas fa-phone text-muted me-1"></i>
                                                            <a href="tel:#(caregiverMatch.caregiver.phone)">#(caregiverMatch.caregiver.phone)</a>
                                                        </div>
                                                    #endif
                                                </div>
                                            </td>
                                            <td>
                                                #if(caregiverMatch.caregiver.location):
                                                    <span class="badge bg-light text-dark">
                                                        <i class="fas fa-map-marker-alt me-1"></i>#(caregiverMatch.caregiver.location)
                                                    </span>
                                                #else:
                                                    <span class="text-muted">Not specified</span>
                                                #endif
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-language me-1"></i>#(caregiverMatch.caregiver.language)
                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-success me-2">#(caregiverMatch.totalConfirmed)</span>
                                                    <small class="text-muted">confirmed</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-2" style="width: 60px; height: 8px;">
                                                        <div class="progress-bar 
                                                            #if(caregiverMatch.bestMatchPercentage >= 80):
                                                                bg-success
                                                            #elseif(caregiverMatch.bestMatchPercentage >= 60):
                                                                bg-warning
                                                            #else:
                                                                bg-danger
                                                            #endif
                                                        " style="width: #(caregiverMatch.bestMatchPercentage)%"></div>
                                                    </div>
                                                    <small class="text-muted">#(caregiverMatch.bestMatchPercentage)%</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="/caregiver/#(caregiverMatch.caregiver.id)" class="btn btn-outline-primary" title="View Caregiver Profile">
                                                        <i class="fas fa-user-nurse"></i>
                                                    </a>
                                                    <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#caregiverMatchModal#(caregiverMatch.caregiver.id)" title="View Confirmed Matches">
                                                        <i class="fas fa-heart"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    #endfor
                                </tbody>
                            </table>
                        </div>
                    #else:
                        <div class="text-center py-5">
                            <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Confirmed Matches</h5>
                            <p class="text-muted">No caregiver matches have been confirmed yet.</p>
                            <div class="mt-3">
                                <a href="/staff/matches" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>View All Matches
                                </a>
                                <a href="/staff/caregivers" class="btn btn-success">
                                    <i class="fas fa-user-nurse me-1"></i>View Caregivers
                                </a>
                            </div>
                        </div>
                    #endif
                </div>
            </div>
        </div>

        <!-- Modals for viewing confirmed matches -->
        #for(caregiverMatch in caregiverMatches):
            <div class="modal fade" id="caregiverMatchModal#(caregiverMatch.caregiver.id)" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-heart text-success me-2"></i>
                                Confirmed Matches for #(caregiverMatch.caregiver.name)
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                #for(clientInfo in caregiverMatch.confirmedClients):
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0">#(clientInfo.client.name)</h6>
                                                    <span class="badge bg-success">#(clientInfo.matchPercentage)%</span>
                                                </div>
                                                <div class="text-sm text-muted mb-2">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    Confirmed: #date(clientInfo.confirmedAt, "MMM d, yyyy")
                                                </div>
                                                #if(clientInfo.notes):
                                                    <div class="text-sm">
                                                        <strong>Notes:</strong> #(clientInfo.notes)
                                                    </div>
                                                #endif
                                                <div class="mt-2">
                                                    <a href="/client/#(clientInfo.client.id)" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-user me-1"></i>View Profile
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                #endfor
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        #endfor
    #endexport
#endextend

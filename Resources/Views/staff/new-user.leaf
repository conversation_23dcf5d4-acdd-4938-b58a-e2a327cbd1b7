#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport
    
    #export("content"):
        <div class="container-fluid">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-plus me-2 text-success"></i>Add New User
                    </h1>
                    <p class="text-muted mb-0">Create a new user account with role-based access</p>
                </div>
                <div>
                    <a href="/staff/users" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Users
                    </a>
                </div>
            </div>

            <!-- Add User Form -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-plus me-2"></i>User Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="createUserForm" action="/staff/users/create" method="POST">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope me-1"></i>Email Address *
                                            </label>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                            <div class="form-text">This will be used for login</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">
                                                <i class="fas fa-phone me-1"></i>Phone Number
                                            </label>
                                            <input type="tel" class="form-control" id="phone" name="phone">
                                            <div class="form-text">Optional contact number</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">
                                                <i class="fas fa-lock me-1"></i>Password *
                                            </label>
                                            <input type="password" class="form-control" id="password" name="password" required minlength="6">
                                            <div class="form-text">Minimum 6 characters</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="confirmPassword" class="form-label">
                                                <i class="fas fa-lock me-1"></i>Confirm Password *
                                            </label>
                                            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required minlength="6">
                                            <div class="form-text">Must match the password above</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="role" class="form-label">
                                        <i class="fas fa-user-tag me-1"></i>User Role *
                                    </label>
                                    <select class="form-select" id="role" name="role" required>
                                        <option value="">Select a role...</option>
                                        <option value="staff">Staff - Full administrative access</option>
                                        <option value="client">Client - Can view matches and register</option>
                                        <option value="caregiver">Caregiver - Can view matches and register</option>
                                    </select>
                                    <div class="form-text">Choose the appropriate access level for this user</div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note:</strong> Staff users will have full access to the admin dashboard and user management. 
                                    Client and Caregiver users will need to complete their respective registration forms after first login.
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="/staff/users" class="btn btn-outline-secondary me-md-2">
                                        <i class="fas fa-times me-1"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-user-plus me-1"></i>Create User
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <style>
            .card {
                border: none;
                border-radius: 10px;
            }
            .card-header {
                border-radius: 10px 10px 0 0 !important;
            }
            .form-control:focus {
                border-color: #28a745;
                box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
            }
            .form-select:focus {
                border-color: #28a745;
                box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
            }
        </style>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const form = document.getElementById('createUserForm');
                const password = document.getElementById('password');
                const confirmPassword = document.getElementById('confirmPassword');

                // Password confirmation validation
                function validatePasswords() {
                    if (password.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('Passwords do not match');
                    } else {
                        confirmPassword.setCustomValidity('');
                    }
                }

                password.addEventListener('input', validatePasswords);
                confirmPassword.addEventListener('input', validatePasswords);

                // Form submission
                form.addEventListener('submit', function(e) {
                    validatePasswords();
                    
                    if (!form.checkValidity()) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                    
                    form.classList.add('was-validated');
                });
            });
        </script>
    #endexport
#endextend

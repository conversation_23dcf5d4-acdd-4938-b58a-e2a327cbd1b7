#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport
    
    #export("content"):
        <div class="container-fluid">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-nurse me-2 text-success"></i>All Caregivers
                    </h1>
                    <p class="text-muted mb-0">Manage and view all registered caregivers</p>
                </div>
                <div>
                    <a href="/staff/dashboard" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                    <a href="/caregiver/register" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>Add New Caregiver
                    </a>
                </div>
            </div>

            <!-- Stats Card -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">#(caregivers.count)</h4>
                                    <p class="mb-0">Total Caregivers</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-nurse fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Caregivers Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Caregiver Directory
                    </h5>
                </div>
                <div class="card-body">
                    #if(caregivers):
                        <div class="table-responsive">
                            <table class="table table-hover" id="caregiversTable">
                                <thead class="table-light">
                                    <tr>
                                        <th class="sortable" data-column="name">
                                            Name <i class="fas fa-sort text-muted"></i>
                                        </th>
                                        <th class="sortable" data-column="email">
                                            Email <i class="fas fa-sort text-muted"></i>
                                        </th>
                                        <th class="sortable" data-column="phone">
                                            Phone <i class="fas fa-sort text-muted"></i>
                                        </th>
                                        <th class="sortable" data-column="location">
                                            Location <i class="fas fa-sort text-muted"></i>
                                        </th>
                                        <th class="sortable" data-column="language">
                                            Language <i class="fas fa-sort text-muted"></i>
                                        </th>
                                        <th class="sortable" data-column="experience">
                                            Experience <i class="fas fa-sort text-muted"></i>
                                        </th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    #for(caregiver in caregivers):
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="fas fa-user-nurse"></i>
                                                    </div>
                                                    <strong>#(caregiver.name)</strong>
                                                </div>
                                            </td>
                                            <td>
                                                #if(caregiver.email):
                                                    <a href="mailto:#(caregiver.email)" class="text-decoration-none">
                                                        #(caregiver.email)
                                                    </a>
                                                #else:
                                                    <span class="text-muted">—</span>
                                                #endif
                                            </td>
                                            <td>
                                                #if(caregiver.phone):
                                                    <a href="tel:#(caregiver.phone)" class="text-decoration-none">
                                                        #(caregiver.phone)
                                                    </a>
                                                #else:
                                                    <span class="text-muted">—</span>
                                                #endif
                                            </td>
                                            <td>
                                                <i class="fas fa-map-marker-alt me-1 text-muted"></i>
                                                #(caregiver.location)
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    #if(caregiver.language == "en"):
                                                        English
                                                    #elseif(caregiver.language == "es"):
                                                        Spanish
                                                    #else:
                                                        #(caregiver.language)
                                                    #endif
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning text-dark">
                                                    #(caregiver.yearsOfExperience) years
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="/caregiver/#(caregiver.id)" class="btn btn-outline-primary" title="View Profile">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="/caregiver/#(caregiver.id)/matches" class="btn btn-outline-success" title="View Matches">
                                                        <i class="fas fa-heart"></i>
                                                    </a>
                                                    <button class="btn btn-outline-danger" title="Delete Caregiver" onclick="deleteCaregiver('#(caregiver.id)', '#(caregiver.name)')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    #endfor
                                </tbody>
                            </table>
                        </div>
                    #else:
                        <div class="text-center py-5">
                            <i class="fas fa-user-nurse fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Caregivers Found</h5>
                            <p class="text-muted">No caregivers have been registered yet.</p>
                            <a href="/caregiver/register" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>Register First Caregiver
                            </a>
                        </div>
                    #endif
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deleteModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirm Deletion</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete caregiver <strong id="caregiverName"></strong>?</p>
                        <p class="text-danger"><small>This action cannot be undone.</small></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmDelete">Delete Caregiver</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let caregiverToDelete = null;

            function deleteCaregiver(caregiverId, caregiverName) {
                caregiverToDelete = caregiverId;
                document.getElementById('caregiverName').textContent = caregiverName;
                new bootstrap.Modal(document.getElementById('deleteModal')).show();
            }

            document.getElementById('confirmDelete').addEventListener('click', function() {
                if (caregiverToDelete) {
                    // TODO: Implement delete functionality
                    alert('Delete functionality will be implemented soon.');
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                }
            });
        </script>

        <script>
            // Table sorting functionality
            document.addEventListener('DOMContentLoaded', function() {
                const table = document.getElementById('caregiversTable');
                if (!table) return;

                const headers = table.querySelectorAll('th.sortable');
                let currentSort = { column: null, direction: 'asc' };

                headers.forEach(header => {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', function() {
                        const column = this.dataset.column;
                        const direction = currentSort.column === column && currentSort.direction === 'asc' ? 'desc' : 'asc';

                        sortTable(table, column, direction);
                        updateSortIcons(headers, column, direction);

                        currentSort = { column, direction };
                    });
                });
            });

            function sortTable(table, column, direction) {
                const tbody = table.querySelector('tbody');
                const rows = Array.from(tbody.querySelectorAll('tr'));

                rows.sort((a, b) => {
                    let aVal = getCellValue(a, column);
                    let bVal = getCellValue(b, column);

                    // Handle numeric sorting for experience
                    if (column === 'experience') {
                        aVal = parseInt(aVal) || 0;
                        bVal = parseInt(bVal) || 0;
                    } else if (typeof aVal === 'string') {
                        aVal = aVal.toLowerCase();
                    }
                    if (typeof bVal === 'string') {
                        bVal = bVal.toLowerCase();
                    }

                    if (direction === 'asc') {
                        return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
                    } else {
                        return aVal < bVal ? 1 : aVal > bVal ? -1 : 0;
                    }
                });

                // Re-append sorted rows
                rows.forEach(row => tbody.appendChild(row));
            }

            function getCellValue(row, column) {
                const columnIndex = getColumnIndex(column);
                const cell = row.cells[columnIndex];

                if (column === 'name') {
                    return cell.querySelector('strong').textContent.trim();
                } else if (column === 'email') {
                    const emailLink = cell.querySelector('a');
                    return emailLink ? emailLink.textContent.trim() : '';
                } else if (column === 'phone') {
                    const phoneLink = cell.querySelector('a');
                    return phoneLink ? phoneLink.textContent.trim() : '';
                } else if (column === 'location') {
                    return cell.textContent.replace(/.*\s/, '').trim(); // Remove icon
                } else if (column === 'language') {
                    return cell.querySelector('.badge').textContent.trim();
                } else if (column === 'experience') {
                    return cell.querySelector('.badge').textContent.trim();
                }

                return cell.textContent.trim();
            }

            function getColumnIndex(column) {
                const columns = ['name', 'email', 'phone', 'location', 'language', 'experience'];
                return columns.indexOf(column);
            }

            function updateSortIcons(headers, activeColumn, direction) {
                headers.forEach(header => {
                    const icon = header.querySelector('i');
                    const column = header.dataset.column;

                    if (column === activeColumn) {
                        icon.className = direction === 'asc' ? 'fas fa-sort-up text-primary' : 'fas fa-sort-down text-primary';
                    } else {
                        icon.className = 'fas fa-sort text-muted';
                    }
                });
            }
        </script>

        <style>
            .avatar-sm {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }

            .sortable {
                user-select: none;
                transition: background-color 0.2s;
            }

            .sortable:hover {
                background-color: rgba(0, 0, 0, 0.05);
            }
        </style>
    #endexport
    
    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend

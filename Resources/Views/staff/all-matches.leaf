#extend("base"):
    #export("header"):
        #extend("shared/header")
    #endexport
    
    #export("content"):
        <div class="container-fluid">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-heart me-2 text-primary"></i>All Client Matches
                    </h1>
                    <p class="text-muted mb-0">Review and manage client-caregiver matches</p>
                </div>
                <div>
                    <a href="/staff/dashboard" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">#(clientMatches.count)</h4>
                                    <p class="mb-0">Clients with Matches</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">
                                        <!-- Total matches will be calculated server-side -->
                                        #(totalMatches)
                                    </h4>
                                    <p class="mb-0">Total Matches</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-heart fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">
                                        #(bestMatchPercentage)%
                                    </h4>
                                    <p class="mb-0">Best Match</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Matches Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Client Match Overview
                    </h5>
                </div>
                <div class="card-body">
                    #if(clientMatches):
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Client</th>
                                        <th>Contact</th>
                                        <th>Location</th>
                                        <th>Language</th>
                                        <th>Matches</th>
                                        <th>Best Match</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    #for(clientMatch in clientMatches):
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <strong>#(clientMatch.client.name)</strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-envelope me-1"></i>
                                                        <a href="mailto:#(clientMatch.client.email)" class="text-decoration-none">
                                                            #(clientMatch.client.email)
                                                        </a>
                                                    </small>
                                                    <small class="text-muted">
                                                        <i class="fas fa-phone me-1"></i>
                                                        <a href="tel:#(clientMatch.client.phone)" class="text-decoration-none">
                                                            #(clientMatch.client.phone)
                                                        </a>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <i class="fas fa-map-marker-alt me-1 text-muted"></i>
                                                #(clientMatch.client.location)
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    #if(clientMatch.client.language == "en"):
                                                        English
                                                    #elseif(clientMatch.client.language == "es"):
                                                        Spanish
                                                    #else:
                                                        #(clientMatch.client.language)
                                                    #endif
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary fs-6">
                                                    #(clientMatch.matchCount) matches
                                                </span>
                                            </td>
                                            <td>
                                                #if(clientMatch.topMatchPercentage > 0):
                                                    <div class="d-flex align-items-center">
                                                        <div class="progress me-2" style="width: 60px; height: 8px;">
                                                            <div class="progress-bar 
                                                                #if(clientMatch.topMatchPercentage >= 80):
                                                                    bg-success
                                                                #elseif(clientMatch.topMatchPercentage >= 60):
                                                                    bg-warning
                                                                #else:
                                                                    bg-danger
                                                                #endif
                                                            " style="width: #(clientMatch.topMatchPercentage)%"></div>
                                                        </div>
                                                        <small class="text-muted">#(clientMatch.topMatchPercentage)%</small>
                                                    </div>
                                                #else:
                                                    <span class="text-muted">No matches</span>
                                                #endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="/client/#(clientMatch.client.id)" class="btn btn-outline-primary" title="View Client Profile">
                                                        <i class="fas fa-user"></i>
                                                    </a>
                                                    #if(clientMatch.matchCount > 0):
                                                        <a href="/client/#(clientMatch.client.id)/matches" class="btn btn-outline-success" title="Review Matches">
                                                            <i class="fas fa-heart"></i>
                                                        </a>
                                                    #else:
                                                        <button class="btn btn-outline-secondary" disabled title="No matches available">
                                                            <i class="fas fa-heart"></i>
                                                        </button>
                                                    #endif
                                                </div>
                                            </td>
                                        </tr>
                                    #endfor
                                </tbody>
                            </table>
                        </div>
                    #else:
                        <div class="text-center py-5">
                            <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Matches Found</h5>
                            <p class="text-muted">No client matches have been generated yet.</p>
                            <div class="mt-3">
                                <a href="/staff/clients" class="btn btn-primary me-2">
                                    <i class="fas fa-users me-1"></i>View Clients
                                </a>
                                <a href="/staff/caregivers" class="btn btn-success">
                                    <i class="fas fa-user-nurse me-1"></i>View Caregivers
                                </a>
                            </div>
                        </div>
                    #endif
                </div>
            </div>
        </div>

        <style>
            .avatar-sm {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }
            
            .progress {
                background-color: #e9ecef;
            }
        </style>
    #endexport
    
    #export("footer"):
        #extend("shared/footer")
    #endexport
#endextend

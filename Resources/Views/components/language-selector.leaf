<!-- Language Selector Component -->
<div class="language-selector">
    <div class="dropdown">
        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-globe me-2"></i>
            <span id="currentLanguage">English</span>
        </button>
        <ul class="dropdown-menu" aria-labelledby="languageDropdown">
            <li><a class="dropdown-item" href="#" onclick="switchLanguage('en')">
                <i class="flag-icon flag-icon-us me-2"></i>English
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="switchLanguage('es')">
                <i class="flag-icon flag-icon-es me-2"></i>Español
            </a></li>
        </ul>
    </div>
</div>

<script>
async function switchLanguage(languageCode) {
    try {
        // Set language preference
        await fetch(`/api/languages/set/${languageCode}`, {
            method: 'POST'
        });
        
        // Update UI
        const languageNames = {
            'en': 'English',
            'es': 'Español'
        };
        
        document.getElementById('currentLanguage').textContent = languageNames[languageCode];
        
        // Store in localStorage for persistence
        localStorage.setItem('preferredLanguage', languageCode);
        
        // Reload page to apply language changes
        window.location.reload();
        
    } catch (error) {
        console.error('Error switching language:', error);
    }
}

// Initialize language on page load
document.addEventListener('DOMContentLoaded', function() {
    const savedLanguage = localStorage.getItem('preferredLanguage') || 'en';
    const languageNames = {
        'en': 'English',
        'es': 'Español'
    };
    
    document.getElementById('currentLanguage').textContent = languageNames[savedLanguage];
});
</script>

<style>
.language-selector {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.flag-icon {
    width: 16px;
    height: 12px;
    background-size: cover;
    display: inline-block;
}

.flag-icon-us {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxNiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjEyIiBmaWxsPSIjQjIyMjM0Ii8+CjxyZWN0IHk9IjEiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB5PSIzIiB3aWR0aD0iMTYiIGhlaWdodD0iMSIgZmlsbD0id2hpdGUiLz4KPHJlY3QgeT0iNSIgd2lkdGg9IjE2IiBoZWlnaHQ9IjEiIGZpbGw9IndoaXRlIi8+CjxyZWN0IHk9IjciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB5PSI5IiB3aWR0aD0iMTYiIGhlaWdodD0iMSIgZmlsbD0id2hpdGUiLz4KPHJlY3QgeT0iMTEiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSI3IiBmaWxsPSIjM0MzQjZFIi8+Cjwvc3ZnPgo=');
}

.flag-icon-es {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxNiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjEyIiBmaWxsPSIjRkZEQTAwIi8+CjxyZWN0IHdpZHRoPSIxNiIgaGVpZ2h0PSIzIiBmaWxsPSIjREEwMjBFIi8+CjxyZWN0IHk9IjkiIHdpZHRoPSIxNiIgaGVpZ2h0PSIzIiBmaWxsPSIjREEwMjBFIi8+Cjwvc3ZnPgo=');
}
</style>

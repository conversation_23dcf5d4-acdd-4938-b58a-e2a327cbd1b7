{"configurations": [{"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:MatchIQ}", "name": "Debug MatchIQ", "program": "${workspaceFolder:MatchIQ}/.build/debug/MatchIQ", "preLaunchTask": "swift: Build Debug MatchIQ"}, {"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:MatchIQ}", "name": "Release MatchIQ", "program": "${workspaceFolder:MatchIQ}/.build/release/MatchIQ", "preLaunchTask": "swift: Build Release MatchIQ"}, {"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:MatchIQ}", "name": "Debug App", "program": "${workspaceFolder:MatchIQ}/.build/debug/App", "preLaunchTask": "swift: Build Debug App"}, {"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:MatchIQ}", "name": "Release App", "program": "${workspaceFolder:MatchIQ}/.build/release/App", "preLaunchTask": "swift: Build Release App"}]}
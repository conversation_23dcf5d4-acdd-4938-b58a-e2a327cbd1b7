import Fluent
import Vapor

func routes(_ app: Application) throws {
    // Health check endpoint for Heroku
    app.get("health") { req async -> [String: String] in
        return [
            "status": "healthy",
            "timestamp": ISO8601DateFormatter().string(from: Date()),
            "environment": app.environment.name
        ]
    }

    app.get("hello") { req async -> String in
        "Hello, world!"
    }

    // Debug route for language testing
    app.get("debug-language") { req in
        return req.view.render("debug-language")
    }

    try app.register(collection: TodoController())
    try app.register(collection: ClientController())
    try app.register(collection: CaregiverController())
    try app.register(collection: QuestionController())
    try app.register(collection: LanguageController())
    try app.register(collection: DatabaseUpdateController())
    try app.register(collection: SeedController())

    // User management API
    try app.register(collection: UserController())

    // Authentication routes
    try app.register(collection: AuthController())

    // Staff routes (protected by role-based access)
    try app.register(collection: StaffC<PERSON>roll<PERSON>())

    try app.register(collection: WebsiteController())
}

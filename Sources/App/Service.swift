//
//  File.swift
//  MatchIQ
//
//  Created by <PERSON> on 4/21/25.
//

import Foundation
import Fluent
import Vapor

struct MatchingService {
    // Calculate match score between client and caregiver for a specific question
    private static func calculateQuestionMatchScore(
        clientOptions: [String],
        caregiverOptions: [String],
        optionScores: [String: Int]
    ) -> Int {
        // Basic implementation - check for matching options
        var score = 0

        // If no options selected for either side, return 0
        if clientOptions.isEmpty || caregiverOptions.isEmpty {
            return 0
        }

        // For each client option, check if caregiver has the same option
        for clientOption in clientOptions {
            if caregiverOptions.contains(clientOption) {
                // If there's a match, add the option's score
                if let optionScore = optionScores[clientOption] {
                    score += optionScore
                }
            }
        }

        // Calculate average score (maximum score is typically 5)
        // This prevents questions with more options from disproportionately affecting the result
        return min(5, score)
    }

    // Calculate category scores and overall match
    static func calculateMatch(
        clientResponses: [ClientResponse],
        caregiverResponses: [CaregiverResponse],
        questions: [Question],
        answerOptions: [AnswerOption]
    ) -> (
        personalityCommunication: Double,
        careNeedsSkills: Double,
        lifestyleInterests: Double,
        culturalLanguageLocation: Double,
        logisticsSchedule: Double,
        weightedTotal: Double,
        percentage: Double
    ) {
        // Convert answer options to dictionary for quick lookup
        var optionScores: [String: Int] = [:]
        for option in answerOptions {
            optionScores[option.optionText] = option.score
        }

        // Create dictionaries for responses
        let clientResponseDict = Dictionary(grouping: clientResponses, by: { $0.questionText })
        let caregiverResponseDict = Dictionary(grouping: caregiverResponses, by: { $0.questionText })

        // Group questions by category
        let questionsByCategory = Dictionary(grouping: questions, by: { $0.scoringCategory })

        // Initialize scores
        var personalityCommunicationScores: [Int] = []
        var careNeedsSkillsScores: [Int] = []
        var lifestyleInterestsScores: [Int] = []
        var culturalLanguageLocationScores: [Int] = []
        var logisticsScheduleScores: [Int] = []

        // Process each question
        for (category, questionsInCategory) in questionsByCategory {
            for question in questionsInCategory {
                let questionText = question.questionText

                guard let clientResponsesForQuestion = clientResponseDict[questionText]?.first,
                      let caregiverResponsesForQuestion = caregiverResponseDict[questionText]?.first else {
                    continue
                }

                let matchScore = calculateQuestionMatchScore(
                    clientOptions: clientResponsesForQuestion.selectedOptions,
                    caregiverOptions: caregiverResponsesForQuestion.selectedOptions,
                    optionScores: optionScores
                )

                // Add score to appropriate category
                switch category {
                case ScoringCategory.personalityAndCommunication:
                    personalityCommunicationScores.append(matchScore)
                case ScoringCategory.careNeedsAndSkills:
                    careNeedsSkillsScores.append(matchScore)
                case ScoringCategory.lifestyleInterests:
                    lifestyleInterestsScores.append(matchScore)
                case ScoringCategory.culturalLanguageLocation:
                    culturalLanguageLocationScores.append(matchScore)
                case ScoringCategory.logisticsAndSchedule:
                    logisticsScheduleScores.append(matchScore)
                default:
                    break
                }
            }
        }

        // Calculate average scores for each category
        let personalityCommunicationAvg = personalityCommunicationScores.isEmpty ? 0 :
            Double(personalityCommunicationScores.reduce(0, +)) / Double(personalityCommunicationScores.count)

        let careNeedsSkillsAvg = careNeedsSkillsScores.isEmpty ? 0 :
            Double(careNeedsSkillsScores.reduce(0, +)) / Double(careNeedsSkillsScores.count)

        let lifestyleInterestsAvg = lifestyleInterestsScores.isEmpty ? 0 :
            Double(lifestyleInterestsScores.reduce(0, +)) / Double(lifestyleInterestsScores.count)

        let culturalLanguageLocationAvg = culturalLanguageLocationScores.isEmpty ? 0 :
            Double(culturalLanguageLocationScores.reduce(0, +)) / Double(culturalLanguageLocationScores.count)

        let logisticsScheduleAvg = logisticsScheduleScores.isEmpty ? 0 :
            Double(logisticsScheduleScores.reduce(0, +)) / Double(logisticsScheduleScores.count)

        // Calculate weighted total score
        let weightedTotal =
            (personalityCommunicationAvg * CategoryWeight.personalityAndCommunication) +
            (careNeedsSkillsAvg * CategoryWeight.careNeedsAndSkills) +
            (lifestyleInterestsAvg * CategoryWeight.lifestyleInterests) +
            (culturalLanguageLocationAvg * CategoryWeight.culturalLanguageLocation) +
            (logisticsScheduleAvg * CategoryWeight.logisticsAndSchedule)

        // Convert to percentage (max score is 5)
        let matchPercentage = (weightedTotal / 5.0) * 100

        return (
            personalityCommunication: personalityCommunicationAvg,
            careNeedsSkills: careNeedsSkillsAvg,
            lifestyleInterests: lifestyleInterestsAvg,
            culturalLanguageLocation: culturalLanguageLocationAvg,
            logisticsSchedule: logisticsScheduleAvg,
            weightedTotal: weightedTotal,
            percentage: matchPercentage
        )
    }

    static func findMatches(
        for client: Client,
        on database: any Database
    ) async throws -> [MatchSummary] {
        // Fetch client responses
        let clientResponses = try await client.$responses.query(on: database).all()

        // Fetch questions and answer options
        async let questions = Question.query(on: database).all()
        async let answerOptions = AnswerOption.query(on: database).all()
        async let caregivers = Caregiver.query(on: database).all()

        let (questionsList, answerOptionList, caregiverList) = try await (questions, answerOptions, caregivers)

        var matchSummaries: [MatchSummary] = []

        for caregiver in caregiverList {
            let caregiverResponses = try await caregiver.$responses.query(on: database).all()

            // Calculate match
            let matchResult = calculateMatch(
                clientResponses: clientResponses,
                caregiverResponses: caregiverResponses,
                questions: questionsList,
                answerOptions: answerOptionList
            )

            // Save match result
            let matchRecord = MatchResult(
                clientID: client.id!,
                caregiverID: caregiver.id!,
                personalityCommunicationScore: matchResult.personalityCommunication,
                careNeedsSkillsScore: matchResult.careNeedsSkills,
                lifestyleInterestsScore: matchResult.lifestyleInterests,
                culturalLanguageLocationScore: matchResult.culturalLanguageLocation,
                logisticsScheduleScore: matchResult.logisticsSchedule,
                weightedTotalScore: matchResult.weightedTotal,
                matchPercentage: matchResult.percentage
            )

            try await matchRecord.save(on: database)

            let summary = MatchSummary(
                caregiverId: caregiver.id!,
                caregiverName: caregiver.name,
                matchPercentage: matchResult.percentage,
                categoryScores: [
                    "personalityCommunication": matchResult.personalityCommunication * 100 / 5.0,
                    "careNeedsSkills": matchResult.careNeedsSkills * 100 / 5.0,
                    "lifestyleInterests": matchResult.lifestyleInterests * 100 / 5.0,
                    "culturalLanguageLocation": matchResult.culturalLanguageLocation * 100 / 5.0,
                    "logisticsSchedule": matchResult.logisticsSchedule * 100 / 5.0
                ]
            )

            matchSummaries.append(summary)
        }

        return matchSummaries.sorted { $0.matchPercentage > $1.matchPercentage }
    }

    // Find client matches for a caregiver (reverse of findMatches)
    static func findClientMatches(
        for caregiver: Caregiver,
        on database: any Database
    ) async throws -> [ClientMatchSummary] {
        // Fetch caregiver responses
        let caregiverResponses = try await caregiver.$responses.query(on: database).all()

        // Fetch questions and answer options
        async let questions = Question.query(on: database).all()
        async let answerOptions = AnswerOption.query(on: database).all()
        async let clients = Client.query(on: database).all()

        let (questionsList, answerOptionList, clientList) = try await (questions, answerOptions, clients)

        var matchSummaries: [ClientMatchSummary] = []

        for client in clientList {
            let clientResponses = try await client.$responses.query(on: database).all()

            // Calculate match (same logic, just swapped parameters)
            let matchResult = calculateMatch(
                clientResponses: clientResponses,
                caregiverResponses: caregiverResponses,
                questions: questionsList,
                answerOptions: answerOptionList
            )

            // Check if match result already exists (to avoid duplicates)
            let existingMatch = try await MatchResult.query(on: database)
                .filter(\.$client.$id == client.id!)
                .filter(\.$caregiver.$id == caregiver.id!)
                .first()

            if existingMatch == nil {
                // Save match result if it doesn't exist
                let matchRecord = MatchResult(
                    clientID: client.id!,
                    caregiverID: caregiver.id!,
                    personalityCommunicationScore: matchResult.personalityCommunication,
                    careNeedsSkillsScore: matchResult.careNeedsSkills,
                    lifestyleInterestsScore: matchResult.lifestyleInterests,
                    culturalLanguageLocationScore: matchResult.culturalLanguageLocation,
                    logisticsScheduleScore: matchResult.logisticsSchedule,
                    weightedTotalScore: matchResult.weightedTotal,
                    matchPercentage: matchResult.percentage
                )

                try await matchRecord.save(on: database)
            }

            let summary = ClientMatchSummary(
                clientId: client.id!,
                clientName: client.name,
                matchPercentage: matchResult.percentage,
                categoryScores: [
                    "personalityCommunication": matchResult.personalityCommunication * 100 / 5.0,
                    "careNeedsSkills": matchResult.careNeedsSkills * 100 / 5.0,
                    "lifestyleInterests": matchResult.lifestyleInterests * 100 / 5.0,
                    "culturalLanguageLocation": matchResult.culturalLanguageLocation * 100 / 5.0,
                    "logisticsSchedule": matchResult.logisticsSchedule * 100 / 5.0
                ]
            )

            matchSummaries.append(summary)
        }

        return matchSummaries.sorted { $0.matchPercentage > $1.matchPercentage }
    }
}

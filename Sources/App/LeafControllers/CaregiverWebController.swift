//
//  File.swift
//  MatchIQ
//
//  Created by <PERSON> on 4/25/25.
//

import Foundation
import Vapor
import Fluent

struct CaregiverWebController: RouteCollection {
    func boot(routes: any RoutesBuilder) throws {
        // GET /caregiver/register
        routes.get("register", use: registerHandler)

        // POST /caregiver/register
        routes.post("register", use: createHandler)

        // GET /caregiver/:caregiverID
        routes.get(":caregiverID", use: showHandler)

        // Match routes - protected by MatchAccessGuard (staff only)
        let matchRoutes = routes.grouped(MatchAccessGuard())

        // GET /caregiver/:caregiverID/matches
        matchRoutes.get(":caregiverID", "matches", use: matchesHandler)

        // GET /caregiver/:caregiverID/matches/empty - Show empty matches state
        matchRoutes.get(":caregiverID", "matches", "empty", use: emptyMatchesHandler)

        // GET /caregiver/:caregiverID/matches/:index - View single match by index
        matchRoutes.get(":caregiverID", "matches", ":index", use: singleMatchHandler)
    }

    // GET /caregiver/register
    func registerHandler(_ req: Request) async throws -> View {
        let context = CaregiverRegistrationContext(
            title: "Caregiver Registration",
            user: req.user,
            isStaff: req.user?.role == .staff
        )
        return try await req.view.render("caregiver/register", context)
    }

    // POST /caregiver/register
    func createHandler(_ req: Request) async throws -> Response {
        // Create caregiver using the API controller
        let caregiverController = CaregiverController()
        let caregiver = try await caregiverController.create(req: req)

        // Redirect to the caregiver's profile page
        return req.redirect(to: "/caregiver/\(caregiver.id!)")
    }

    // GET /caregiver/:caregiverID
    func showHandler(_ req: Request) async throws -> View {
        guard let caregiverIDString = req.parameters.get("caregiverID"),
              let caregiverID = UUID(caregiverIDString) else {
            throw Abort(.badRequest, reason: "Invalid caregiver ID")
        }

        guard let caregiver = try await Caregiver.find(caregiverID, on: req.db) else {
            throw Abort(.notFound, reason: "Caregiver not found")
        }

        // Get caregiver responses
        let responses = try await CaregiverResponse.query(on: req.db)
            .filter(\.$caregiver.$id == caregiverID)
            .all()


        let context = CaregiverProfileContext(
            title: "Caregiver Profile",
            caregiver: caregiver,
            responses: responses,
            user: req.user,
            isStaff: req.user?.role == .staff
        )

        return try await req.view.render("caregiver/profile", context)
    }

    // GET /caregiver/:caregiverID/matches - Redirect to first match or show empty state
    func matchesHandler(_ req: Request) async throws -> Response {
        guard let caregiverIDString = req.parameters.get("caregiverID"),
              let caregiverID = UUID(caregiverIDString) else {
            throw Abort(.badRequest, reason: "Invalid caregiver ID")
        }

        guard let caregiver = try await Caregiver.find(caregiverID, on: req.db) else {
            throw Abort(.notFound, reason: "Caregiver not found")
        }

        let matches = try await MatchingService.findClientMatches(for: caregiver, on: req.db)

        if matches.isEmpty {
            // If no matches, redirect to empty state view
            return req.redirect(to: "/caregiver/\(caregiverID)/matches/empty")
        }

        // Redirect to first match
        return req.redirect(to: "/caregiver/\(caregiverID)/matches/0")
    }

    // GET /caregiver/:caregiverID/matches/empty - Show empty matches state
    func emptyMatchesHandler(_ req: Request) async throws -> View {
        guard let caregiverIDString = req.parameters.get("caregiverID"),
              let caregiverID = UUID(caregiverIDString) else {
            throw Abort(.badRequest, reason: "Invalid caregiver ID")
        }

        guard let caregiver = try await Caregiver.find(caregiverID, on: req.db) else {
            throw Abort(.notFound, reason: "Caregiver not found")
        }

        let context = CaregiverMatchesContext(title: "Client Matches", caregiver: caregiver, matches: [])
        return try await req.view.render("caregiver/matches-empty", context)
    }

    // GET /caregiver/:caregiverID/matches/:index - View single match by index
    func singleMatchHandler(_ req: Request) async throws -> View {
        guard let caregiverIDString = req.parameters.get("caregiverID"),
              let caregiverID = UUID(caregiverIDString),
              let indexString = req.parameters.get("index"),
              let index = Int(indexString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        guard let caregiver = try await Caregiver.find(caregiverID, on: req.db) else {
            throw Abort(.notFound, reason: "Caregiver not found")
        }

        let matches = try await MatchingService.findClientMatches(for: caregiver, on: req.db)

        guard index >= 0 && index < matches.count else {
            throw Abort(.badRequest, reason: "Invalid match index")
        }

        let match = matches[index]

        // Get the client details
        guard let client = try await Client.find(match.clientId, on: req.db) else {
            throw Abort(.notFound, reason: "Client not found")
        }

        // Get client responses
        let clientResponses = try await client.$responses.query(on: req.db).all()

        // Format the match for display
        let formattedMatch = ClientMatchViewModel(
            clientId: match.clientId,
            clientName: match.clientName,
            matchPercentage: Int(match.matchPercentage.rounded()),
            categoryScores: MatchCategoryScores(
                personalityCommunication: Int(match.categoryScores["personalityCommunication"] ?? 0),
                careNeedsSkills: Int(match.categoryScores["careNeedsSkills"] ?? 0),
                lifestyleInterests: Int(match.categoryScores["lifestyleInterests"] ?? 0),
                culturalLanguageLocation: Int(match.categoryScores["culturalLanguageLocation"] ?? 0),
                logisticsSchedule: Int(match.categoryScores["logisticsSchedule"] ?? 0)
            )
        )

        let context = CaregiverSingleMatchContext(
            title: "Client Match",
            caregiver: caregiver,
            match: formattedMatch,
            client: client,
            clientResponses: clientResponses,
            currentIndex: index,
            totalMatches: matches.count,
            hasNext: index < matches.count - 1,
            hasPrevious: index > 0,
            confirmationStatus: nil // Caregivers don't confirm matches, only view them
        )

        return try await req.view.render("caregiver/single-match", context)
    }
}

// Context for caregiver registration page
struct CaregiverRegistrationContext: Content {
    let title: String
    let user: User?
    let isStaff: Bool
}

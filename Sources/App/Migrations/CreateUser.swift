import Fluent

struct CreateUser: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema("users")
            .id()
            .field("email", .string, .required)
            .field("phone", .string)
            .field("password_hash", .string, .required)
            .field("role", .string, .required)
            .field("client_id", .uuid, .references("clients", "id"))
            .field("caregiver_id", .uuid, .references("caregivers", "id"))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .unique(on: "email")
            .create()
    }

    func revert(on database: Database) async throws {
        try await database.schema("users").delete()
    }
}

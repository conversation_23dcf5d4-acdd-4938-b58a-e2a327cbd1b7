import Fluent

struct UpdateClientWithEmailPhone: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema("clients")
            .field("email", .string)
            .field("phone", .string)
            .update()
    }

    func revert(on database: Database) async throws {
        try await database.schema("clients")
            .deleteField("email")
            .deleteField("phone")
            .update()
    }
}

import Fluent

struct AddStaffValidationToMatchConfirmation: AsyncMigration {
    func prepare(on database: any Database) async throws {
        try await database.schema("match_confirmations")
            .field("staff_validated", .bool, .required, .custom("DEFAULT FALSE"))
            .field("validated_by_staff_id", .uuid, .references("users", "id", onDelete: .setNull))
            .field("staff_validated_at", .datetime)
            .field("staff_validation_notes", .string)
            .update()
    }

    func revert(on database: any Database) async throws {
        try await database.schema("match_confirmations")
            .deleteField("staff_validated")
            .deleteField("validated_by_staff_id")
            .deleteField("staff_validated_at")
            .deleteField("staff_validation_notes")
            .update()
    }
}

import Fluent

struct AddLanguageToClientsAndCaregivers: AsyncMigration {
    func prepare(on database: Database) async throws {
        // Add language column to clients table
        try await database.schema("clients")
            .field("language", .string, .required, .custom("DEFAULT 'en'"))
            .update()
        
        // Add language column to caregivers table
        try await database.schema("caregivers")
            .field("language", .string, .required, .custom("DEFAULT 'en'"))
            .update()
    }

    func revert(on database: Database) async throws {
        // Remove language column from clients table
        try await database.schema("clients")
            .deleteField("language")
            .update()
        
        // Remove language column from caregivers table
        try await database.schema("caregivers")
            .deleteField("language")
            .update()
    }
}

import Fluent
import Vapor

struct UpdateCertificationOptions: AsyncMigration {
    func prepare(on database: any Database) async throws {
        // Update the Certifications question to replace CPR/First Aid, Dementia Care, and None (willing to train) with RN
        if let certificationQuestion = try await Question.query(on: database)
            .filter(\.$questionText == "Certifications")
            .first() {
            
            // Update the options array
            certificationQuestion.options = ["CNA", "HHA", "RN"]
            try await certificationQuestion.save(on: database)
        }
        
        // Remove old answer options for Certifications
        try await AnswerOption.query(on: database)
            .filter(\.$questionText == "Certifications")
            .filter(\.$optionText ~~ ["CPR/First Aid", "Dementia Care", "None (willing to train)"])
            .delete()
        
        // Add new RN answer option
        let rnOption = AnswerOption(questionText: "Certifications", optionText: "RN", score: 5)
        try await rnOption.save(on: database)
    }
    
    func revert(on database: any Database) async throws {
        // Revert back to original options
        if let certificationQuestion = try await Question.query(on: database)
            .filter(\.$questionText == "Certifications")
            .first() {
            
            certificationQuestion.options = ["CNA", "HHA", "CPR/First Aid", "Dementia Care", "None (willing to train)"]
            try await certificationQuestion.save(on: database)
        }
        
        // Remove RN option
        try await AnswerOption.query(on: database)
            .filter(\.$questionText == "Certifications")
            .filter(\.$optionText == "RN")
            .delete()
        
        // Add back the old options
        let oldOptions = [
            AnswerOption(questionText: "Certifications", optionText: "CPR/First Aid", score: 5),
            AnswerOption(questionText: "Certifications", optionText: "Dementia Care", score: 5),
            AnswerOption(questionText: "Certifications", optionText: "None (willing to train)", score: 5)
        ]
        
        for option in oldOptions {
            try await option.save(on: database)
        }
    }
}

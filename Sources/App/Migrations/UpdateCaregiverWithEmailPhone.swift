import Fluent

struct UpdateCaregiverWithEmailPhone: AsyncMigration {
    func prepare(on database: Database) async throws {
        try await database.schema("caregivers")
            .field("email", .string)
            .field("phone", .string)
            .update()
    }

    func revert(on database: Database) async throws {
        try await database.schema("caregivers")
            .deleteField("email")
            .deleteField("phone")
            .update()
    }
}

import Fluent
import Vapor

struct CreateLanguageSupport: AsyncMigration {
    func prepare(on database: any Database) async throws {
        // Create languages table
        try await database.schema("languages")
            .id()
            .field("code", .string, .required)
            .field("name", .string, .required)
            .field("is_default", .bool, .required)
            .unique(on: "code")
            .create()
        
        // Create question_translations table
        try await database.schema("question_translations")
            .id()
            .field("question_id", .uuid, .required, .references("questions", "id", onDelete: .cascade))
            .field("language_id", .uuid, .required, .references("languages", "id", onDelete: .cascade))
            .field("translated_text", .string, .required)
            .field("translated_options", .array(of: .string), .required)
            .unique(on: "question_id", "language_id")
            .create()
        
        // Seed default languages
        let english = Language(code: "en", name: "English", isDefault: true)
        let spanish = Language(code: "es", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", isDefault: false)
        
        try await english.save(on: database)
        try await spanish.save(on: database)
        
        // Get all existing questions for translation
        let questions = try await Question.query(on: database).all()
        
        // Add Spanish translations for all questions
        for question in questions {
            let spanishTranslation = try await createSpanishTranslation(for: question, spanishLanguage: spanish)
            try await spanishTranslation.save(on: database)
        }
    }
    
    func revert(on database: any Database) async throws {
        try await database.schema("question_translations").delete()
        try await database.schema("languages").delete()
    }
    
    private func createSpanishTranslation(for question: Question, spanishLanguage: Language) async throws -> QuestionTranslation {
        let translations = getSpanishTranslations()
        
        let translatedText = translations[question.questionText]?.text ?? question.questionText
        let translatedOptions = translations[question.questionText]?.options ?? question.options
        
        return QuestionTranslation(
            questionID: question.id!,
            languageID: spanishLanguage.id!,
            translatedText: translatedText,
            translatedOptions: translatedOptions
        )
    }
    
    private func getSpanishTranslations() -> [String: (text: String, options: [String])] {
        return [
            "Preferred personality type": (
                text: "Tipo de personalidad preferido",
                options: ["Tranquilo y silencioso", "Extrovertido y hablador", "Equilibrado y adaptable", "Enérgico y entusiasta", "Paciente y reservado"]
            ),
            "Preferred communication style": (
                text: "Estilo de comunicación preferido",
                options: ["Instrucciones directas y claras", "Casual y conversacional", "Con paciencia y explicaciones detalladas", "Usando ayudas visuales o demostraciones"]
            ),
            "Preferred care type": (
                text: "Tipo de cuidado preferido",
                options: ["Cuidado personal", "Compañía", "Manejo de medicamentos", "Limpieza ligera", "Preparación de comidas", "Transporte"]
            ),
            "Preferred schedule": (
                text: "Horario preferido",
                options: ["Mañana (6 AM - 12 PM)", "Tarde (12 PM - 6 PM)", "Noche (6 PM - 12 AM)", "Overnight (12 AM - 6 AM)", "Flexible"]
            ),
            "Certifications": (
                text: "Certificaciones",
                options: ["CNA", "HHA", "RN"]
            ),
            "Medical comfort level": (
                text: "Nivel de comodidad médica",
                options: ["Transferencias de elevación/movilidad", "Deterioro cognitivo/cuidado de la memoria", "Equipo médico (tubos, colostomía)", "Compañía emocional"]
            ),
            "Preferred activities": (
                text: "Actividades preferidas",
                options: ["Lectura", "Música", "Ejercicio ligero", "Juegos", "Artesanías", "Jardinería", "Cocinar", "Ver televisión"]
            ),
            "Preferred location": (
                text: "Ubicación preferida",
                options: ["Centro de la ciudad", "Suburbios", "Lado norte", "Lado sur", "Lado este", "Lado oeste"]
            ),
            "Language preference": (
                text: "Preferencia de idioma",
                options: ["Inglés", "Español", "Francés", "Mandarín", "Alemán", "Italiano"]
            ),
            "Cultural background": (
                text: "Antecedentes culturales",
                options: ["Americano", "Hispano/Latino", "Asiático", "Afroamericano", "Europeo", "Medio Oriente", "Nativo Americano", "Mixto", "Otro"]
            ),
            "Preferred gender": (
                text: "Género preferido",
                options: ["Masculino", "Femenino", "Sin preferencia"]
            ),
            "Experience level": (
                text: "Nivel de experiencia",
                options: ["0-1 años", "1-3 años", "3-5 años", "5-10 años", "10+ años"]
            ),
            "Availability": (
                text: "Disponibilidad",
                options: ["Tiempo completo", "Medio tiempo", "Según sea necesario", "Fines de semana solamente", "Días de semana solamente"]
            ),
            "Transportation": (
                text: "Transporte",
                options: ["Tengo vehículo propio", "Transporte público", "Necesito transporte", "Puedo caminar/bicicleta"]
            ),
            "Rate preference": (
                text: "Preferencia de tarifa",
                options: ["$15-20/hora", "$20-25/hora", "$25-30/hora", "$30-35/hora", "$35+/hora"]
            )
        ]
    }
}

import Vapor
import Fluent
import Crypto

struct AuthController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let auth = routes.grouped("auth")
        
        // Login routes
        auth.get("login", use: loginPage)
        auth.post("login", use: login)
        
        // Logout route
        auth.post("logout", use: logout)
        
        // Registration routes
        auth.get("register", use: registerPage)
        auth.post("register", use: register)

        // Status route
        auth.get("status", use: status)
    }
    
    // GET /auth/login
    func loginPage(req: Request) async throws -> View {
        return try await req.view.render("auth/login", ["title": "Login"])
    }
    
    // POST /auth/login
    func login(req: Request) async throws -> Response {
        let loginData = try req.content.decode(LoginRequest.self)
        
        // Find user by email
        guard let user = try await User.query(on: req.db)
            .filter(\.$email == loginData.email.lowercased())
            .first() else {
            throw Abort(.unauthorized, reason: "Invalid email or password")
        }
        
        // Verify password
        guard try Bcrypt.verify(loginData.password, created: user.passwordHash) else {
            throw Abort(.unauthorized, reason: "Invalid email or password")
        }
        
        // Create session token
        let tokenValue = [UInt8].random(count: 32).base64
        let token = UserToken(value: tokenValue, userID: user.id!)
        try await token.save(on: req.db)
        
        // Store token in session
        req.session.data["auth_token"] = tokenValue
        
        // Redirect based on role
        switch user.role {
        case .staff:
            return req.redirect(to: "/staff/dashboard")
        case .client:
            return req.redirect(to: "/client/dashboard")
        case .caregiver:
            return req.redirect(to: "/caregiver/dashboard")
        }
    }
    
    // POST /auth/logout
    func logout(req: Request) async throws -> Response {
        // Remove token from database if exists
        if let tokenValue = req.session.data["auth_token"] {
            try await UserToken.query(on: req.db)
                .filter(\.$value == tokenValue)
                .delete()
        }
        
        // Clear session
        req.session.destroy()
        
        return req.redirect(to: "/")
    }
    
    // GET /auth/register
    func registerPage(req: Request) async throws -> View {
        return try await req.view.render("auth/register", ["title": "Register"])
    }
    
    // POST /auth/register
    func register(req: Request) async throws -> Response {
        let registerData = try req.content.decode(RegisterRequest.self)
        
        // Validate input
        try registerData.validate()
        
        // Check for duplicate email
        if let _ = try await User.query(on: req.db)
            .filter(\.$email == registerData.email.lowercased())
            .first() {
            throw Abort(.badRequest, reason: "An account already exists with this email.")
        }
        
        // Check for duplicate phone if provided
        if let phone = registerData.phone, !phone.isEmpty {
            if let _ = try await User.query(on: req.db)
                .filter(\.$phone == phone)
                .first() {
                throw Abort(.badRequest, reason: "An account already exists with this phone number.")
            }
        }
        
        // Hash password
        let passwordHash = try Bcrypt.hash(registerData.password)
        
        // Create user
        let user = User(
            email: registerData.email.lowercased(),
            phone: registerData.phone,
            passwordHash: passwordHash,
            role: registerData.role
        )
        try await user.save(on: req.db)
        
        // Create session token
        let tokenValue = [UInt8].random(count: 32).base64
        let token = UserToken(value: tokenValue, userID: user.id!)
        try await token.save(on: req.db)
        
        // Store token in session
        req.session.data["auth_token"] = tokenValue
        
        // Redirect to appropriate registration form
        switch user.role {
        case .client:
            return req.redirect(to: "/client/register")
        case .caregiver:
            return req.redirect(to: "/caregiver/register")
        case .staff:
            return req.redirect(to: "/staff/dashboard")
        }
    }

    // GET /auth/status
    func status(req: Request) async throws -> AuthStatusResponse {
        if let user = req.user {
            return AuthStatusResponse(
                authenticated: true,
                user: UserInfo(
                    id: user.id,
                    email: user.email,
                    role: user.role.rawValue
                )
            )
        } else {
            return AuthStatusResponse(authenticated: false, user: nil)
        }
    }
}

// Request DTOs
struct LoginRequest: Content {
    let email: String
    let password: String
}

struct RegisterRequest: Content {
    let email: String
    let phone: String?
    let password: String
    let confirmPassword: String
    let role: UserRole
    
    func validate() throws {
        guard !email.isEmpty else {
            throw Abort(.badRequest, reason: "Email is required")
        }
        
        guard email.contains("@") else {
            throw Abort(.badRequest, reason: "Invalid email format")
        }
        
        guard password.count >= 6 else {
            throw Abort(.badRequest, reason: "Password must be at least 6 characters")
        }
        
        guard password == confirmPassword else {
            throw Abort(.badRequest, reason: "Passwords do not match")
        }
    }
}

// Response DTOs
struct AuthStatusResponse: Content {
    let authenticated: Bool
    let user: UserInfo?
}

struct UserInfo: Content {
    let id: UUID?
    let email: String
    let role: String
}

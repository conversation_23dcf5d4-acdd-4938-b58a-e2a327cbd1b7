import Vapor
import Fluent
import Crypto

struct UserController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let users = routes.grouped("api", "users")
        
        // User registration endpoint
        users.post("register", use: register)
    }
    
    // POST /api/users/register
    func register(req: Request) async throws -> UserRegistrationResponse {
        let registerData = try req.content.decode(APIUserRegistrationRequest.self)
        
        // Validate input
        try registerData.validate()
        
        // Check for duplicate email
        if let _ = try await User.query(on: req.db)
            .filter(\.$email == registerData.email.lowercased())
            .first() {
            throw Abort(.badRequest, reason: "An account already exists with this email.")
        }
        
        // Hash password
        let passwordHash = try Bcrypt.hash(registerData.password)
        
        // Create user
        let user = User(
            email: registerData.email.lowercased(),
            phone: nil,
            passwordHash: passwordHash,
            role: registerData.role
        )
        try await user.save(on: req.db)
        
        return UserRegistrationResponse(
            success: true,
            message: "User created successfully",
            user: UserInfo(
                id: user.id,
                email: user.email,
                role: user.role.rawValue
            )
        )
    }
}

// Request DTO for API registration
struct APIUserRegistrationRequest: Content {
    let email: String
    let password: String
    let role: UserRole
    
    func validate() throws {
        guard !email.isEmpty else {
            throw Abort(.badRequest, reason: "Email is required")
        }
        
        guard email.contains("@") else {
            throw Abort(.badRequest, reason: "Invalid email format")
        }
        
        guard password.count >= 6 else {
            throw Abort(.badRequest, reason: "Password must be at least 6 characters")
        }
    }
}

// Response DTO
struct UserRegistrationResponse: Content {
    let success: Bool
    let message: String
    let user: UserInfo
}

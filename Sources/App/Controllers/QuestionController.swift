//
//  QuestionsController.swift
//  MatchIQ
//
//  Created by <PERSON> on 4/21/25.
//

import Fluent
import Vapor

struct QuestionController: RouteCollection {

    func boot(routes: any RoutesBuilder) throws {
        let questions = routes.grouped("questions")

        // Get all questions
        questions.get(use: index)

        // Get questions by category
        questions.get("category", ":category", use: getByCategory)

        // Get all answer options
        questions.get("options", use: getAnswerOptions)

        // Get localized questions
        questions.get("localized", use: getLocalizedQuestions)
        questions.get("localized", ":languageCode", use: getQuestionsInLanguage)
    }
    
    // GET /questions
    @Sendable
    func index(req: Request) async throws ->[Question] {
        try await Question.query(on: req.db).all()
    }
    
    // GET /questions/category/:category
    @Sendable
    func getByCategory(req: Request) async throws -> [Question] {
        guard let category = req.parameters.get("category") else {
            throw Abort(.badRequest, reason: "Category parameter is required")
        }
        
        return try await Question.query(on: req.db)
            .filter(\.$scoringCategory == category)
            .all()
    }
    
    // GET /questions/options
    @Sendable
    func getAnswerOptions(req: Request) async throws -> [AnswerOption] {
        try await AnswerOption.query(on: req.db).all()
    }

    // GET /questions/localized
    @Sendable
    func getLocalizedQuestions(req: Request) async throws -> [LocalizedQuestion] {
        let languageCode = req.currentLanguage
        return try await getQuestionsInLanguage(req: req, languageCode: languageCode)
    }

    // GET /questions/localized/:languageCode
    @Sendable
    func getQuestionsInLanguage(req: Request) async throws -> [LocalizedQuestion] {
        let languageCode = req.parameters.get("languageCode") ?? req.currentLanguage
        return try await getQuestionsInLanguage(req: req, languageCode: languageCode)
    }

    private func getQuestionsInLanguage(req: Request, languageCode: String) async throws -> [LocalizedQuestion] {
        let questions = try await Question.query(on: req.db).all()
        var localizedQuestions: [LocalizedQuestion] = []

        for question in questions {
            let (text, options) = try await question.getLocalizedContent(for: languageCode, on: req.db)

            let localizedQuestion = LocalizedQuestion(
                id: question.id,
                questionText: text,
                scoringCategory: question.scoringCategory,
                options: options,
                language: languageCode
            )
            localizedQuestions.append(localizedQuestion)
        }

        return localizedQuestions
    }
}

import Fluent
import Vapor

struct LanguageController: RouteCollection {
    func boot(routes: any RoutesBuilder) throws {
        let languages = routes.grouped("api", "languages")
        
        // Get all available languages
        languages.get(use: getLanguages)
        
        // Get questions in specific language
        languages.get(":languageCode", "questions", use: getQuestionsInLanguage)
        
        // Set language preference (could be stored in session/cookie)
        languages.post("set", ":languageCode", use: setLanguage)
    }
    
    // GET /api/languages
    @Sendable
    func getLanguages(req: Request) async throws -> [LanguageResponse] {
        // Return hardcoded languages for testing
        return [
            LanguageResponse(code: "en", name: "English", isDefault: true),
            LanguageResponse(code: "es", name: "Español", isDefault: false)
        ]
    }
    
    // GET /api/languages/:languageCode/questions
    @Sendable
    func getQuestionsInLanguage(req: Request) async throws -> [LocalizedQuestion] {
        guard let languageCode = req.parameters.get("languageCode") else {
            throw Abort(.badRequest, reason: "Language code is required")
        }

        let questions = try await Question.query(on: req.db).all()
        var localizedQuestions: [LocalizedQuestion] = []

        for question in questions {
            var questionText = question.questionText
            var options = question.options

            // Apply Spanish translations if available
            if languageCode == "es" {
                let translations = getSpanishTranslations()
                if let translation = translations[question.questionText] {
                    if let spanishText = translation["text"] as? String {
                        questionText = spanishText
                    }
                    if let spanishOptions = translation["options"] as? [String] {
                        options = spanishOptions
                    }
                }
            }

            let localizedQuestion = LocalizedQuestion(
                id: question.id,
                questionText: questionText,
                scoringCategory: question.scoringCategory,
                options: options,
                language: languageCode
            )
            localizedQuestions.append(localizedQuestion)
        }

        return localizedQuestions
    }
    
    // POST /api/languages/set/:languageCode
    @Sendable
    func setLanguage(req: Request) async throws -> HTTPStatus {
        guard let languageCode = req.parameters.get("languageCode") else {
            throw Abort(.badRequest, reason: "Language code is required")
        }

        // Validate language code
        guard ["en", "es"].contains(languageCode) else {
            throw Abort(.badRequest, reason: "Unsupported language code")
        }

        // Store language preference in session
        req.session.data["language"] = languageCode

        return .ok
    }

    private func getSpanishTranslations() -> [String: [String: Any]] {
        return [
            "Preferred personality type": [
                "text": "Tipo de personalidad preferido",
                "options": ["Tranquilo y silencioso", "Extrovertido y hablador", "Equilibrado y adaptable", "Enérgico y entusiasta", "Paciente y reservado"]
            ],
            "Preferred communication style": [
                "text": "Estilo de comunicación preferido",
                "options": ["Instrucciones directas y claras", "Casual y conversacional", "Con paciencia y explicaciones detalladas", "Usando ayudas visuales o demostraciones"]
            ],
            "Approach to care": [
                "text": "Enfoque del cuidado",
                "options": ["Toma la iniciativa", "Sigue las instrucciones de cerca", "Enfoque equilibrado (proactivo y receptivo)"]
            ],
            "Top values in interaction": [
                "text": "Valores principales en la interacción",
                "options": ["Empatía", "Humor", "Paciencia", "Proactividad", "Flexibilidad", "Atención al detalle"]
            ],
            "Comfort with emotional situations": [
                "text": "Comodidad con situaciones emocionales",
                "options": ["Mantener la calma y paciencia", "Usar humor para aliviar la tensión", "Redirigir el enfoque a temas positivos", "Fomentar técnicas de relajación", "Prefiero no involucrarme profundamente en asuntos emocionales"]
            ],
            "Hobbies and activities": [
                "text": "Pasatiempos y actividades",
                "options": ["Jardinería", "Lectura", "Ver TV/Películas", "Cocinar/Hornear", "Juegos de mesa o rompecabezas", "Caminar/Actividades al aire libre", "Artes y manualidades", "Música y canto", "Asistir a eventos sociales"]
            ],
            "Religious or spiritual importance": [
                "text": "Importancia religiosa o espiritual",
                "options": ["Muy importante", "Algo importante", "No importante"]
            ],
            "Comfort with pets": [
                "text": "Comodidad con mascotas",
                "options": ["Amo las mascotas", "Cómodo, con algunas alergias/preferencias", "Prefiero sin mascotas"]
            ],
            "Medical comfort level": [
                "text": "Nivel de comodidad médica",
                "options": ["Transferencias de elevación/movilidad", "Deterioro cognitivo/cuidado de la memoria", "Equipo médico (tubos, colostomía)", "Compañía emocional"]
            ],
            "Language preference": [
                "text": "Preferencia de idioma",
                "options": ["Inglés", "Español", "Francés", "Otro (Especificar)"]
            ],
            "Location preference": [
                "text": "Preferencia de ubicación",
                "options": ["Dentro de 5 millas", "Dentro de 10 millas", "Dentro de 20 millas", "Sin preferencia"]
            ],
            "Cultural familiarity": [
                "text": "Familiaridad cultural",
                "options": ["Muy familiarizado con el cuidado multicultural", "Algo familiarizado", "No familiarizado, pero abierto a aprender"]
            ],
            "Dietary/cultural accommodations": [
                "text": "Acomodaciones dietéticas/culturales",
                "options": ["Importante, por favor especificar", "No se necesitan acomodaciones especiales"]
            ],
            "Preferred schedule": [
                "text": "Horario preferido",
                "options": ["Tiempo completo", "Medio tiempo", "Cuidado nocturno", "Disponible/flexible"]
            ],
            "Certifications": [
                "text": "Certificaciones",
                "options": ["CNA", "HHA", "RN"]
            ]
        ]
    }
}

// Extension to get current language from session
extension Request {
    var currentLanguage: String {
        return session.data["language"] ?? "en"
    }
}

import Vapor
import Fluent

struct SeedController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let seedRoutes = routes.grouped("api", "seed")

        seedRoutes.get("", use: seedStatus)
        seedRoutes.post("all", use: seedAll)
        seedRoutes.post("clients", use: seedClients)
        seedRoutes.post("caregivers", use: seedCaregivers)
        seedRoutes.delete("clear", use: clearData)
        seedRoutes.post("reset", use: resetAndSeed)

        // Admin UI routes
        let adminRoutes = routes.grouped("admin")
        adminRoutes.get("seed", use: seedAdminPage)
    }

    // MARK: - API Endpoints

    func seedStatus(req: Request) async throws -> SeedStatusResponse {
        let clientCount = try await Client.query(on: req.db).count()
        let caregiverCount = try await Caregiver.query(on: req.db).count()
        let questionCount = try await Question.query(on: req.db).count()
        let clientResponseCount = try await ClientResponse.query(on: req.db).count()
        let caregiverResponseCount = try await CaregiverResponse.query(on: req.db).count()

        return SeedStatusResponse(
            clients: clientCount,
            caregivers: caregiverCount,
            questions: questionCount,
            clientResponses: clientResponseCount,
            caregiverResponses: caregiverResponseCount,
            message: "Database status retrieved successfully"
        )
    }

    func seedAll(req: Request) async throws -> SeedResponse {
        let startTime = Date()

        // Check if questions exist
        let questionCount = try await Question.query(on: req.db).count()
        guard questionCount > 0 else {
            throw Abort(.badRequest, reason: "No questions found. Please run migrations first.")
        }

        try await SeedData.createSeedData(app: req.application)

        let duration = Date().timeIntervalSince(startTime)
        let clientCount = try await Client.query(on: req.db).count()
        let caregiverCount = try await Caregiver.query(on: req.db).count()

        return SeedResponse(
            success: true,
            message: "Successfully seeded \(clientCount) clients and \(caregiverCount) caregivers",
            clientsCreated: SeedData.clientNames.count,
            caregiversCreated: SeedData.caregiverNames.count,
            duration: duration
        )
    }

    func seedClients(req: Request) async throws -> SeedResponse {
        let startTime = Date()

        let questions = try await Question.query(on: req.db).all()
        guard !questions.isEmpty else {
            throw Abort(.badRequest, reason: "No questions found. Please run migrations first.")
        }

        // Create clients
        for name in SeedData.clientNames {
            let client = Client(name: name)
            try await client.save(on: req.db)

            // Create responses for each question
            for question in questions {
                let response = ClientResponse(
                    clientID: try client.requireID(),
                    questionText: question.questionText,
                    selectedOptions: SeedData.getRandomResponse(for: question.questionText, isClient: true)
                )
                try await response.save(on: req.db)
            }
        }

        let duration = Date().timeIntervalSince(startTime)

        return SeedResponse(
            success: true,
            message: "Successfully seeded \(SeedData.clientNames.count) clients",
            clientsCreated: SeedData.clientNames.count,
            caregiversCreated: 0,
            duration: duration
        )
    }

    func seedCaregivers(req: Request) async throws -> SeedResponse {
        let startTime = Date()

        let questions = try await Question.query(on: req.db).all()
        guard !questions.isEmpty else {
            throw Abort(.badRequest, reason: "No questions found. Please run migrations first.")
        }

        // Create caregivers
        for name in SeedData.caregiverNames {
            let caregiver = Caregiver(name: name)
            try await caregiver.save(on: req.db)

            // Create responses for each question
            for question in questions {
                let response = CaregiverResponse(
                    caregiverID: try caregiver.requireID(),
                    questionText: question.questionText,
                    selectedOptions: SeedData.getRandomResponse(for: question.questionText, isClient: false)
                )
                try await response.save(on: req.db)
            }
        }

        let duration = Date().timeIntervalSince(startTime)

        return SeedResponse(
            success: true,
            message: "Successfully seeded \(SeedData.caregiverNames.count) caregivers",
            clientsCreated: 0,
            caregiversCreated: SeedData.caregiverNames.count,
            duration: duration
        )
    }

    func clearData(req: Request) async throws -> SeedResponse {
        let startTime = Date()

        // Delete in order to respect foreign key constraints
        try await ClientResponse.query(on: req.db).delete()
        try await CaregiverResponse.query(on: req.db).delete()
        try await Client.query(on: req.db).delete()
        try await Caregiver.query(on: req.db).delete()

        let duration = Date().timeIntervalSince(startTime)

        return SeedResponse(
            success: true,
            message: "Successfully cleared all client and caregiver data",
            clientsCreated: 0,
            caregiversCreated: 0,
            duration: duration
        )
    }

    func resetAndSeed(req: Request) async throws -> SeedResponse {
        let startTime = Date()

        // Clear existing data
        try await ClientResponse.query(on: req.db).delete()
        try await CaregiverResponse.query(on: req.db).delete()
        try await Client.query(on: req.db).delete()
        try await Caregiver.query(on: req.db).delete()

        // Seed new data
        try await SeedData.createSeedData(app: req.application)

        let duration = Date().timeIntervalSince(startTime)

        return SeedResponse(
            success: true,
            message: "Successfully reset and seeded database",
            clientsCreated: SeedData.clientNames.count,
            caregiversCreated: SeedData.caregiverNames.count,
            duration: duration
        )
    }

    // MARK: - Admin UI

    func seedAdminPage(req: Request) async throws -> View {
        let status = try await seedStatus(req: req)

        let context = SeedAdminContext(
            title: "MatchIQ Admin",
            status: status,
            user: req.user,
            isStaff: req.user?.role == .staff
        )

        return try await req.view.render("admin/seed", context)
    }
}

// MARK: - Response Models

struct SeedResponse: Content {
    let success: Bool
    let message: String
    let clientsCreated: Int
    let caregiversCreated: Int
    let duration: TimeInterval
}

struct SeedStatusResponse: Content {
    let clients: Int
    let caregivers: Int
    let questions: Int
    let clientResponses: Int
    let caregiverResponses: Int
    let message: String
}

struct SeedAdminContext: Content {
    let title: String
    let status: SeedStatusResponse
    let user: User?
    let isStaff: Bool
}

import Vapor
import Fluent

struct StaffController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let staff = routes.grouped("staff")
        let protected = staff.grouped(StaffGuard())
        
        // Dashboard
        protected.get("dashboard", use: dashboard)
        
        // Match management routes
        protected.get("matches", use: allMatches)
        protected.get("matches", "client", ":clientID", use: clientMatches)
        protected.get("matches", "client", ":clientID", ":index", use: viewMatch)
        protected.post("matches", "client", ":clientID", ":index", "reject", use: rejectMatch)
        protected.post("matches", "client", ":clientID", ":index", "confirm", use: confirmMatch)
        
        // User management
        protected.get("users", use: allUsers)
        protected.get("clients", use: allClients)
        protected.get("caregivers", use: allCaregivers)
    }
    
    // GET /staff/dashboard
    func dashboard(req: Request) async throws -> View {
        let user = try req.requireStaff()
        
        // Get summary statistics
        let clientCount = try await Client.query(on: req.db).count()
        let caregiverCount = try await Caregiver.query(on: req.db).count()
        let userCount = try await User.query(on: req.db).count()
        
        let context = StaffDashboardContext(
            title: "Staff Dashboard",
            user: user,
            clientCount: clientCount,
            caregiverCount: caregiverCount,
            userCount: userCount
        )
        
        return try await req.view.render("staff/dashboard", context)
    }
    
    // GET /staff/matches
    func allMatches(req: Request) async throws -> View {
        let user = try req.requireStaff()

        // Get all clients with their match counts
        let clients = try await Client.query(on: req.db).all()
        var clientMatchSummaries: [StaffClientMatchSummary] = []
        var totalMatches = 0
        var bestMatchPercentage = 0.0

        for client in clients {
            let matches = try await MatchingService.findMatches(for: client, on: req.db)
            let topMatch = matches.first?.matchPercentage ?? 0

            clientMatchSummaries.append(StaffClientMatchSummary(
                client: client,
                matchCount: matches.count,
                topMatchPercentage: topMatch
            ))

            totalMatches += matches.count
            if topMatch > bestMatchPercentage {
                bestMatchPercentage = topMatch
            }
        }

        let context = AllMatchesContext(
            title: "All Client Matches",
            user: user,
            clientMatches: clientMatchSummaries,
            totalMatches: totalMatches,
            bestMatchPercentage: Int(bestMatchPercentage)
        )

        return try await req.view.render("staff/all-matches", context)
    }
    
    // GET /staff/matches/client/:clientID
    func clientMatches(req: Request) async throws -> Response {
        let user = try req.requireStaff()
        
        guard let clientIDString = req.parameters.get("clientID"),
              let clientID = UUID(clientIDString) else {
            throw Abort(.badRequest, reason: "Invalid client ID")
        }
        
        guard let client = try await Client.find(clientID, on: req.db) else {
            throw Abort(.notFound, reason: "Client not found")
        }
        
        let matches = try await MatchingService.findMatches(for: client, on: req.db)
        
        if matches.isEmpty {
            return req.redirect(to: "/staff/matches")
        }
        
        // Redirect to first match
        return req.redirect(to: "/staff/matches/client/\(clientID)/0")
    }
    
    // GET /staff/matches/client/:clientID/:index
    func viewMatch(req: Request) async throws -> View {
        let user = try req.requireStaff()
        
        guard let clientIDString = req.parameters.get("clientID"),
              let clientID = UUID(clientIDString),
              let indexString = req.parameters.get("index"),
              let index = Int(indexString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }
        
        guard let client = try await Client.find(clientID, on: req.db) else {
            throw Abort(.notFound, reason: "Client not found")
        }
        
        let matches = try await MatchingService.findMatches(for: client, on: req.db)
        
        guard index >= 0 && index < matches.count else {
            throw Abort(.badRequest, reason: "Invalid match index")
        }
        
        let match = matches[index]
        
        // Get caregiver and responses
        guard let caregiver = try await Caregiver.find(match.caregiverId, on: req.db) else {
            throw Abort(.notFound, reason: "Caregiver not found")
        }
        
        let caregiverResponses = try await caregiver.$responses.query(on: req.db).all()
        
        let context = StaffMatchContext(
            title: "Match Review",
            user: user,
            client: client,
            caregiver: caregiver,
            match: match,
            caregiverResponses: caregiverResponses,
            currentIndex: index,
            totalMatches: matches.count,
            hasNext: index < matches.count - 1,
            hasPrevious: index > 0
        )
        
        return try await req.view.render("staff/match-review", context)
    }
    
    // POST /staff/matches/client/:clientID/:index/reject
    func rejectMatch(req: Request) async throws -> Response {
        let user = try req.requireStaff()
        
        guard let clientIDString = req.parameters.get("clientID"),
              let clientID = UUID(clientIDString),
              let indexString = req.parameters.get("index"),
              let index = Int(indexString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }
        
        // Log the rejection (you could store this in a database table)
        req.logger.info("Staff user \(user.email) rejected match for client \(clientID) at index \(index)")
        
        // Get matches to determine next action
        guard let client = try await Client.find(clientID, on: req.db) else {
            throw Abort(.notFound, reason: "Client not found")
        }
        
        let matches = try await MatchingService.findMatches(for: client, on: req.db)
        let nextIndex = index + 1
        
        // If there's a next match, redirect to it
        if nextIndex < matches.count {
            return req.redirect(to: "/staff/matches/client/\(clientID)/\(nextIndex)")
        } else {
            // No more matches, redirect back to all matches
            return req.redirect(to: "/staff/matches")
        }
    }
    
    // POST /staff/matches/client/:clientID/:index/confirm
    func confirmMatch(req: Request) async throws -> Response {
        let user = try req.requireStaff()
        
        guard let clientIDString = req.parameters.get("clientID"),
              let clientID = UUID(clientIDString),
              let indexString = req.parameters.get("index"),
              let index = Int(indexString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }
        
        // Log the confirmation
        req.logger.info("Staff user \(user.email) confirmed match for client \(clientID) at index \(index)")
        
        // Here you could save the confirmed match to a database table
        // For now, just redirect back to all matches
        return req.redirect(to: "/staff/matches")
    }
    
    // GET /staff/clients
    func allClients(req: Request) async throws -> View {
        let user = try req.requireStaff()
        let clients = try await Client.query(on: req.db).all()
        
        let context = StaffClientsContext(
            title: "All Clients",
            user: user,
            clients: clients
        )
        
        return try await req.view.render("staff/clients", context)
    }
    
    // GET /staff/caregivers
    func allCaregivers(req: Request) async throws -> View {
        let user = try req.requireStaff()
        let caregivers = try await Caregiver.query(on: req.db).all()
        
        let context = StaffCaregiversContext(
            title: "All Caregivers",
            user: user,
            caregivers: caregivers
        )
        
        return try await req.view.render("staff/caregivers", context)
    }
    
    // GET /staff/users
    func allUsers(req: Request) async throws -> View {
        let user = try req.requireStaff()
        let users = try await User.query(on: req.db)
            .with(\.$client)
            .with(\.$caregiver)
            .all()

        // Calculate role counts
        let clientCount = users.filter { $0.role == .client }.count
        let caregiverCount = users.filter { $0.role == .caregiver }.count
        let staffCount = users.filter { $0.role == .staff }.count

        let context = StaffUsersContext(
            title: "All Users",
            user: user,
            users: users,
            clientCount: clientCount,
            caregiverCount: caregiverCount,
            staffCount: staffCount
        )

        return try await req.view.render("staff/users", context)
    }
}

// Context structs for views
struct StaffDashboardContext: Codable {
    let title: String
    let user: User
    let clientCount: Int
    let caregiverCount: Int
    let userCount: Int
}

struct AllMatchesContext: Codable {
    let title: String
    let user: User
    let clientMatches: [StaffClientMatchSummary]
    let totalMatches: Int
    let bestMatchPercentage: Int
}

struct StaffClientMatchSummary: Codable {
    let client: Client
    let matchCount: Int
    let topMatchPercentage: Double
}

struct StaffMatchContext: Codable {
    let title: String
    let user: User
    let client: Client
    let caregiver: Caregiver
    let match: MatchSummary
    let caregiverResponses: [CaregiverResponse]
    let currentIndex: Int
    let totalMatches: Int
    let hasNext: Bool
    let hasPrevious: Bool
}

struct StaffClientsContext: Codable {
    let title: String
    let user: User
    let clients: [Client]
}

struct StaffCaregiversContext: Codable {
    let title: String
    let user: User
    let caregivers: [Caregiver]
}

struct StaffUsersContext: Codable {
    let title: String
    let user: User
    let users: [User]
    let clientCount: Int
    let caregiverCount: Int
    let staffCount: Int
}

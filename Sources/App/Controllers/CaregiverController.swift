//
//  File.swift
//  MatchIQ
//
//  Created by <PERSON> on 4/21/25.
//

import Foundation
import Vapor
import Fluent

struct CaregiverController: RouteCollection {
    func boot(routes: any RoutesBuilder) throws {
        let caregivers = routes.grouped("caregivers")

        // Get all caregivers
        caregivers.get(use: index)

        // Get a specific caregiver
        caregivers.get(":caregiverID", use: show)

        // Create a new caregiver with form responses
        caregivers.post(use: create)

        // Delete a caregiver
        caregivers.delete(":caregiverID", use: delete)
    }

    // GET /caregivers
    @Sendable
    func index(req: Request) async throws -> [Caregiver] {
        try await Caregiver.query(on: req.db).all()
    }

    // GET /caregivers/:caregiverID
    @Sendable
    func show(req: Request) async throws -> Caregiver {
        guard let caregiverID = req.parameters.get("caregiverID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid caregiver ID")
        }

        guard let client = try await Caregiver.find(caregiverID, on: req.db) else {
            throw Abort(.notFound, reason: "Caregiver not found")
        }

        return client
    }

    // POST /caregivers
    @Sendable
    func create(req: Request) async throws -> Caregiver {
        let formSubmission = try req.content.decode(CaregiverFormSubmission.self)

        // Check for duplicate name (case-insensitive)
        let normalizedName = formSubmission.name.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()

        // Check existing caregivers for duplicate name
        let existingCaregivers = try await Caregiver.query(on: req.db).all()
        for caregiver in existingCaregivers {
            if caregiver.name.lowercased() == normalizedName {
                throw Abort(.badRequest, reason: "A caregiver with the name '\(formSubmission.name)' is already registered in the system.")
            }
        }

        // Also check if a client exists with the same name
        let existingClients = try await Client.query(on: req.db).all()
        for client in existingClients {
            if client.name.lowercased() == normalizedName {
                throw Abort(.badRequest, reason: "A client with the name '\(formSubmission.name)' is already registered in the system. Please use a different name or contact support.")
            }
        }

        // Check for duplicate email if provided
        if let email = formSubmission.email, !email.isEmpty {
            let normalizedEmail = email.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

            // Check caregivers
            if let _ = try await Caregiver.query(on: req.db)
                .filter(\.$email == normalizedEmail)
                .first() {
                throw Abort(.badRequest, reason: "A caregiver account already exists with the email '\(email)'. Please use a different email or contact support if this is your account.")
            }

            // Check clients
            if let _ = try await Client.query(on: req.db)
                .filter(\.$email == normalizedEmail)
                .first() {
                throw Abort(.badRequest, reason: "A client account already exists with the email '\(email)'. Please use a different email or contact support if this is your account.")
            }
        }

        // Check for duplicate phone if provided
        if let phone = formSubmission.phone, !phone.isEmpty {
            let normalizedPhone = phone.trimmingCharacters(in: .whitespacesAndNewlines)

            // Check caregivers
            if let _ = try await Caregiver.query(on: req.db)
                .filter(\.$phone == normalizedPhone)
                .first() {
                throw Abort(.badRequest, reason: "A caregiver account already exists with the phone number '\(phone)'. Please use a different phone number or contact support if this is your account.")
            }

            // Check clients
            if let _ = try await Client.query(on: req.db)
                .filter(\.$phone == normalizedPhone)
                .first() {
                throw Abort(.badRequest, reason: "A client account already exists with the phone number '\(phone)'. Please use a different phone number or contact support if this is your account.")
            }
        }

        // Create caregiver
        let caregiver = Caregiver(
            name: formSubmission.name,
            language: formSubmission.language,
            email: formSubmission.email?.lowercased(),
            phone: formSubmission.phone
        )
        try await caregiver.create(on: req.db)

        // Save all responses
        for response in formSubmission.responses {
            let caregiverResponse = CaregiverResponse(
                caregiverID: caregiver.id!,
                questionText: response.questionText,
                selectedOptions: response.selectedOptions
            )
            try await caregiverResponse.save(on: req.db)
        }

        return caregiver
    }

    // DELETE /caregivers/:caregiverID
    @Sendable
    func delete(req: Request) async throws -> HTTPStatus {
        guard let caregiverID = req.parameters.get("caregiverID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid caregiver ID")
        }

        guard let caregiver = try await Caregiver.find(caregiverID, on: req.db) else {
            throw Abort(.notFound, reason: "Caregiver not found")
        }

        // Delete all caregiver responses first (due to foreign key constraints)
        try await CaregiverResponse.query(on: req.db)
            .filter(\.$caregiver.$id == caregiverID)
            .delete()

        // Delete the caregiver
        try await caregiver.delete(on: req.db)

        return .noContent
    }

    @Sendable
    func update(req: Request)  async throws {
        guard let caregiverID = req.parameters.get("caregiverID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid caregiver ID")
        }

        guard let _ = try await Caregiver.find(caregiverID, on: req.db) else {
            throw Abort(.notFound, reason: "Caregiver not found")
        }

        try await CaregiverResponse.query(on: req.db).filter(\.$caregiver.$id == caregiverID).all().delete(on: req.db)


    }
}



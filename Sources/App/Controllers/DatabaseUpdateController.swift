import Fluent
import Vapor

struct DatabaseUpdateController: RouteCollection {
    func boot(routes: any RoutesBuilder) throws {
        let admin = routes.grouped("admin", "database")
        
        // Update certification options
        admin.post("update-certifications", use: updateCertifications)
        
        // Create language tables and data
        admin.post("setup-languages", use: setupLanguages)
    }
    
    // POST /admin/database/update-certifications
    @Sendable
    func updateCertifications(req: Request) async throws -> HTTPStatus {
        // Update the Certifications question to replace old options with RN
        if let certificationQuestion = try await Question.query(on: req.db)
            .filter(\.$questionText == "Certifications")
            .first() {
            
            // Update the options array
            certificationQuestion.options = ["CNA", "HHA", "RN"]
            try await certificationQuestion.save(on: req.db)
        }
        
        return .ok
    }
    
    // POST /admin/database/setup-languages
    @Sendable
    func setupLanguages(req: Request) async throws -> HTTPStatus {
        // Create a simple in-memory translation system for testing
        // This will be stored in the session for demonstration

        // Just set a flag that translations are loaded
        req.session.data["spanish_translations_loaded"] = "true"

        return .ok
    }
    
    private func getSpanishTranslations() -> [String: [String: Any]] {
        return [
            "Preferred personality type": [
                "text": "Tipo de personalidad preferido",
                "options": ["Tranquilo y silencioso", "Extrovertido y hablador", "Equilibrado y adaptable", "Enérgico y entusiasta", "Paciente y reservado"]
            ],
            "Preferred communication style": [
                "text": "Estilo de comunicación preferido",
                "options": ["Instrucciones directas y claras", "Casual y conversacional", "Con paciencia y explicaciones detalladas", "Usando ayudas visuales o demostraciones"]
            ],
            "Approach to care": [
                "text": "Enfoque del cuidado",
                "options": ["Toma la iniciativa", "Sigue las instrucciones de cerca", "Enfoque equilibrado (proactivo y receptivo)"]
            ],
            "Top values in interaction": [
                "text": "Valores principales en la interacción",
                "options": ["Empatía", "Humor", "Paciencia", "Proactividad", "Flexibilidad", "Atención al detalle"]
            ],
            "Comfort with emotional situations": [
                "text": "Comodidad con situaciones emocionales",
                "options": ["Mantener la calma y paciencia", "Usar humor para aliviar la tensión", "Redirigir el enfoque a temas positivos", "Fomentar técnicas de relajación", "Prefiero no involucrarme profundamente en asuntos emocionales"]
            ],
            "Hobbies and activities": [
                "text": "Pasatiempos y actividades",
                "options": ["Jardinería", "Lectura", "Ver TV/Películas", "Cocinar/Hornear", "Juegos de mesa o rompecabezas", "Caminar/Actividades al aire libre", "Artes y manualidades", "Música y canto", "Asistir a eventos sociales"]
            ],
            "Religious or spiritual importance": [
                "text": "Importancia religiosa o espiritual",
                "options": ["Muy importante", "Algo importante", "No importante"]
            ],
            "Comfort with pets": [
                "text": "Comodidad con mascotas",
                "options": ["Amo las mascotas", "Cómodo, con algunas alergias/preferencias", "Prefiero sin mascotas"]
            ],
            "Medical comfort level": [
                "text": "Nivel de comodidad médica",
                "options": ["Transferencias de elevación/movilidad", "Deterioro cognitivo/cuidado de la memoria", "Equipo médico (tubos, colostomía)", "Compañía emocional"]
            ],
            "Language preference": [
                "text": "Preferencia de idioma",
                "options": ["Inglés", "Español", "Francés", "Otro (Especificar)"]
            ],
            "Location preference": [
                "text": "Preferencia de ubicación",
                "options": ["Dentro de 5 millas", "Dentro de 10 millas", "Dentro de 20 millas", "Sin preferencia"]
            ],
            "Cultural familiarity": [
                "text": "Familiaridad cultural",
                "options": ["Muy familiarizado con el cuidado multicultural", "Algo familiarizado", "No familiarizado, pero abierto a aprender"]
            ],
            "Dietary/cultural accommodations": [
                "text": "Acomodaciones dietéticas/culturales",
                "options": ["Importante, por favor especificar", "No se necesitan acomodaciones especiales"]
            ],
            "Preferred schedule": [
                "text": "Horario preferido",
                "options": ["Tiempo completo", "Medio tiempo", "Cuidado nocturno", "Disponible/flexible"]
            ],
            "Certifications": [
                "text": "Certificaciones",
                "options": ["CNA", "HHA", "RN"]
            ]
        ]
    }

    private func getSpanishTranslation(for questionText: String, options: [String]) -> (String, [String]) {
        let translations = getSpanishTranslations()

        if let translation = translations[questionText],
           let text = translation["text"] as? String,
           let translatedOptions = translation["options"] as? [String] {
            return (text, translatedOptions)
        }

        // Fallback to English if no translation found
        return (questionText, options)
    }
}

struct LanguageID: Content {
    let id: UUID
}

import Vapor
import Fluent

struct SeedData {

    // Sample names for realistic data
    static let clientNames = [
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON> Hall", "<PERSON>", "Brandon Young", "Megan King",
        "<PERSON> Wright", "<PERSON> Lopez", "Justin Hill", "<PERSON><PERSON> Scott", "<PERSON> Green"
    ]

    static let caregiverNames = [
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Taylor", "<PERSON> Thomas",
        "<PERSON> Jackson", "Harper White", "Lucas Harris", "<PERSON> Martin", "<PERSON>",
        "<PERSON>", "<PERSON> Walker", "<PERSON> Hall", "<PERSON>", "<PERSON>",
        "Owen King", "<PERSON>", "<PERSON>", "<PERSON> Hill", "<PERSON> Scott"
    ]

    // Sample responses for different question types
    static let carePreferences = [
        ["Personal Care", "Medication Management"],
        ["Companionship", "Light Housekeeping"],
        ["Meal Preparation", "Transportation"],
        ["Personal Care", "Companionship", "Medication Management"],
        ["Light Housekeeping", "Meal Preparation"],
        ["Transportation", "Personal Care"],
        ["Companionship", "Light Housekeeping", "Meal Preparation"],
        ["Medication Management", "Transportation"],
        ["Personal Care", "Companionship"],
        ["All of the above"]
    ]

    static let schedulePreferences = [
        ["Morning (6 AM - 12 PM)"],
        ["Afternoon (12 PM - 6 PM)"],
        ["Evening (6 PM - 12 AM)"],
        ["Morning (6 AM - 12 PM)", "Afternoon (12 PM - 6 PM)"],
        ["Afternoon (12 PM - 6 PM)", "Evening (6 PM - 12 AM)"],
        ["Morning (6 AM - 12 PM)", "Evening (6 PM - 12 AM)"],
        ["Flexible - any time"],
        ["Weekdays only"],
        ["Weekends only"],
        ["24/7 availability"]
    ]

    static let locationPreferences = [
        ["Downtown"],
        ["Suburbs"],
        ["North Side"],
        ["South Side"],
        ["East Side"],
        ["West Side"],
        ["Downtown", "North Side"],
        ["Suburbs", "South Side"],
        ["East Side", "West Side"],
        ["Anywhere in the city"]
    ]

    static let experienceLevel = [
        ["0-1 years"],
        ["1-3 years"],
        ["3-5 years"],
        ["5-10 years"],
        ["10+ years"],
        ["New to caregiving"],
        ["Some experience"],
        ["Very experienced"],
        ["Professional certification"],
        ["Healthcare background"]
    ]

    static let specialNeeds = [
        ["Dementia/Alzheimer's"],
        ["Mobility assistance"],
        ["Diabetes management"],
        ["Post-surgery recovery"],
        ["Mental health support"],
        ["Physical therapy assistance"],
        ["Stroke recovery"],
        ["Chronic pain management"],
        ["No special needs"],
        ["Multiple conditions"]
    ]

    static func createSeedData(app: Application) async throws {
        // First, get all questions to create realistic responses
        let questions = try await Question.query(on: app.db).all()

        guard !questions.isEmpty else {
            app.logger.warning("No questions found in database. Please seed questions first.")
            return
        }

        // Create clients
        app.logger.info("Creating seed clients...")
        for (index, name) in clientNames.enumerated() {
            let client = Client(name: name)
            try await client.save(on: app.db)

            // Create responses for each question
            for question in questions {
                let response = ClientResponse(
                    clientID: try client.requireID(),
                    questionText: question.questionText,
                    selectedOptions: getRandomResponse(for: question.questionText, isClient: true)
                )
                try await response.save(on: app.db)
            }

            if index % 5 == 0 {
                app.logger.info("Created \(index + 1) clients...")
            }
        }

        // Create caregivers
        app.logger.info("Creating seed caregivers...")
        for (index, name) in caregiverNames.enumerated() {
            let caregiver = Caregiver(name: name)
            try await caregiver.save(on: app.db)

            // Create responses for each question
            for question in questions {
                let response = CaregiverResponse(
                    caregiverID: try caregiver.requireID(),
                    questionText: question.questionText,
                    selectedOptions: getRandomResponse(for: question.questionText, isClient: false)
                )
                try await response.save(on: app.db)
            }

            if index % 5 == 0 {
                app.logger.info("Created \(index + 1) caregivers...")
            }
        }

        app.logger.info("Seed data creation completed!")
        app.logger.info("Created \(clientNames.count) clients and \(caregiverNames.count) caregivers")
    }

    static func getRandomResponse(for questionText: String, isClient: Bool) -> [String] {
        let lowercaseQuestion = questionText.lowercased()

        // Match responses based on question content
        if lowercaseQuestion.contains("care") && lowercaseQuestion.contains("need") {
            return carePreferences.randomElement() ?? ["Personal Care"]
        } else if lowercaseQuestion.contains("schedule") || lowercaseQuestion.contains("time") {
            return schedulePreferences.randomElement() ?? ["Flexible - any time"]
        } else if lowercaseQuestion.contains("location") || lowercaseQuestion.contains("area") {
            return locationPreferences.randomElement() ?? ["Downtown"]
        } else if lowercaseQuestion.contains("experience") {
            return experienceLevel.randomElement() ?? ["Some experience"]
        } else if lowercaseQuestion.contains("special") || lowercaseQuestion.contains("condition") {
            return specialNeeds.randomElement() ?? ["No special needs"]
        } else if lowercaseQuestion.contains("budget") || lowercaseQuestion.contains("rate") {
            let rates = ["$15-20/hour", "$20-25/hour", "$25-30/hour", "$30-35/hour", "$35+/hour"]
            return [rates.randomElement() ?? "$20-25/hour"]
        } else if lowercaseQuestion.contains("language") {
            let languages = ["English", "Spanish", "French", "Mandarin", "German", "Italian"]
            return [languages.randomElement() ?? "English"]
        } else if lowercaseQuestion.contains("certification") {
            let certifications = ["CNA", "HHA", "RN"]
            return [certifications.randomElement() ?? "CNA"]
        } else {
            // Default responses for any other questions
            let defaultResponses = [
                ["Yes"], ["No"], ["Sometimes"], ["Prefer not to say"],
                ["Very important"], ["Somewhat important"], ["Not important"],
                ["Excellent"], ["Good"], ["Fair"], ["Needs improvement"]
            ]
            return defaultResponses.randomElement() ?? ["Yes"]
        }
    }
}

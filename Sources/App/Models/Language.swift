import Fluent
import Vapor

// Language Model for multi-language support
final class Language: Model, Content, @unchecked Sendable {
    static let schema = "languages"
    
    @ID(key: .id)
    var id: UUID?
    
    @Field(key: "code")
    var code: String // e.g., "en", "es"
    
    @Field(key: "name")
    var name: String // e.g., "English", "Spanish"
    
    @Field(key: "is_default")
    var isDefault: Bool
    
    init() { }
    
    init(id: UUID? = nil, code: String, name: String, isDefault: Bool = false) {
        self.id = id
        self.code = code
        self.name = name
        self.isDefault = isDefault
    }
}

// Question Translation Model
final class QuestionTranslation: Model, Content, @unchecked Sendable {
    static let schema = "question_translations"
    
    @ID(key: .id)
    var id: UUID?
    
    @Parent(key: "question_id")
    var question: Question
    
    @Parent(key: "language_id")
    var language: Language
    
    @Field(key: "translated_text")
    var translatedText: String
    
    @Field(key: "translated_options")
    var translatedOptions: [String]
    
    init() { }
    
    init(id: UUID? = nil, questionID: UUID, languageID: UUID, translatedText: String, translatedOptions: [String]) {
        self.id = id
        self.$question.id = questionID
        self.$language.id = languageID
        self.translatedText = translatedText
        self.translatedOptions = translatedOptions
    }
}

// Extended Question model with language support
extension Question {
    func getTranslation(for languageCode: String, on database: Database) async throws -> QuestionTranslation? {
        guard let language = try await Language.query(on: database)
            .filter(\.$code == languageCode)
            .first() else {
            return nil
        }
        
        return try await QuestionTranslation.query(on: database)
            .filter(\.$question.$id == self.id!)
            .filter(\.$language.$id == language.id!)
            .first()
    }
    
    func getLocalizedContent(for languageCode: String, on database: Database) async throws -> (text: String, options: [String]) {
        if languageCode == "en" {
            return (text: self.questionText, options: self.options)
        }
        
        if let translation = try await getTranslation(for: languageCode, on: database) {
            return (text: translation.translatedText, options: translation.translatedOptions)
        }
        
        // Fallback to English if translation not found
        return (text: self.questionText, options: self.options)
    }
}

// Language Support DTO
struct LocalizedQuestion: Content {
    let id: UUID?
    let questionText: String
    let scoringCategory: String
    let options: [String]
    let language: String
}

struct LanguageResponse: Content {
    let code: String
    let name: String
    let isDefault: Bool
}

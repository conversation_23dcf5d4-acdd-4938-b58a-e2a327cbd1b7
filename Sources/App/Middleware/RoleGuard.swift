import Vapor

// Middleware to guard routes based on user roles
struct RoleGuard: AsyncMiddleware {
    let requiredRole: UserRole
    
    init(requiredRole: UserRole) {
        self.requiredRole = requiredRole
    }
    
    func respond(to request: Request, chainingTo next: AsyncResponder) async throws -> Response {
        guard let user = request.user else {
            throw Abort(.unauthorized, reason: "Authentication required")
        }
        
        guard user.role == requiredRole else {
            throw Abort(.forbidden, reason: "Access denied. Required role: \(requiredRole.rawValue)")
        }
        
        return try await next.respond(to: request)
    }
}

// Convenience middleware for staff-only routes
struct StaffGuard: AsyncMiddleware {
    func respond(to request: Request, chainingTo next: AsyncResponder) async throws -> Response {
        return try await RoleGuard(requiredRole: .staff).respond(to: request, chainingTo: next)
    }
}

// Middleware to prevent access to match-related routes for non-staff users
struct MatchAccessGuard: AsyncMiddleware {
    func respond(to request: Request, chainingTo next: AsyncResponder) async throws -> Response {
        guard let user = request.user else {
            throw Abort(.unauthorized, reason: "Authentication required")
        }
        
        // Only staff can access match-related functionality
        guard user.role == .staff else {
            // Redirect non-staff users to appropriate page
            if user.role == .client {
                return request.redirect(to: "/client/dashboard")
            } else if user.role == .caregiver {
                return request.redirect(to: "/caregiver/dashboard")
            } else {
                throw Abort(.forbidden, reason: "Access denied")
            }
        }
        
        return try await next.respond(to: request)
    }
}

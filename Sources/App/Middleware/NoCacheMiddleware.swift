import Vapor

struct NoCacheMiddleware: AsyncMiddleware {
    func respond(to request: Request, chainingTo next: AsyncResponder) async throws -> Response {
        let response = try await next.respond(to: request)
        
        // Add no-cache headers for CSS and JS files
        if request.url.path.hasSuffix(".css") || request.url.path.hasSuffix(".js") {
            response.headers.add(name: .cacheControl, value: "no-cache, no-store, must-revalidate")
            response.headers.add(name: "Prag<PERSON>", value: "no-cache")
            response.headers.add(name: "Expires", value: "0")
        }
        
        return response
    }
}

# Heroku Deployment Guide for MatchIQ

This guide will walk you through deploying MatchIQ to Heroku.

## Prerequisites

1. **Heroku CLI** - Install from [heroku.com/cli](https://devcenter.heroku.com/articles/heroku-cli)
2. **Git** - Ensure your project is in a Git repository
3. **Heroku Account** - Sign up at [heroku.com](https://heroku.com)

## Quick Deploy

### Option 1: One-Click Deploy
Click the button below to deploy directly to Heroku:

[![Deploy](https://www.herokucdn.com/deploy/button.svg)](https://heroku.com/deploy)

### Option 2: Manual Deploy

#### Step 1: Login to Heroku
```bash
heroku login
```

#### Step 2: Create Heroku App
```bash
heroku create your-app-name
```

#### Step 3: Add PostgreSQL Database
```bash
heroku addons:create heroku-postgresql:essential-0
```

#### Step 4: Set Environment Variables
```bash
heroku config:set SWIFT_VERSION=5.10
heroku config:set VAPOR_ENV=production
```

#### Step 5: Deploy
```bash
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

**Note**: If you encounter build errors, the deployment may take several minutes. Check the build logs:
```bash
heroku logs --tail
```

#### Step 6: Run Database Migrations
```bash
heroku run vapor run migrate --yes
```

## Configuration

### Environment Variables
The following environment variables are automatically configured:
- `DATABASE_URL` - PostgreSQL connection string (auto-configured by Heroku)
- `PORT` - Server port (auto-configured by Heroku)
- `SWIFT_VERSION` - Swift version (6.0)
- `VAPOR_ENV` - Environment (production)

### Custom Configuration
You can override default settings:
```bash
heroku config:set DATABASE_HOST=custom-host
heroku config:set DATABASE_PORT=5432
heroku config:set DATABASE_USERNAME=username
heroku config:set DATABASE_PASSWORD=password
heroku config:set DATABASE_NAME=database
```

## Scaling

### Scale Web Dynos
```bash
heroku ps:scale web=1
```

### Upgrade Database
```bash
heroku addons:upgrade heroku-postgresql:standard-0
```

## Monitoring

### View Logs
```bash
heroku logs --tail
```

### Check App Status
```bash
heroku ps
```

### Open App
```bash
heroku open
```

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Swift version compatibility
   - Verify all dependencies are included
   - Review build logs: `heroku logs --tail`

2. **Database Connection Issues**
   - Verify PostgreSQL addon is attached
   - Check DATABASE_URL is set: `heroku config`
   - Run migrations: `heroku run vapor run migrate --yes`

3. **App Won't Start**
   - Check Procfile configuration
   - Verify port binding in configure.swift
   - Review application logs

### Debug Commands
```bash
# Check configuration
heroku config

# Check running processes
heroku ps

# Access app shell
heroku run bash

# Check database
heroku pg:info

# Reset database (WARNING: destroys all data)
heroku pg:reset DATABASE_URL --confirm your-app-name
heroku run vapor run migrate --yes
```

## Production Considerations

### Security
- Environment variables are automatically secured
- Database connections use SSL by default
- CORS is configured for production

### Performance
- Use at least Basic dyno for production
- Consider upgrading PostgreSQL plan for better performance
- Monitor app metrics in Heroku dashboard

### Backup
- Heroku PostgreSQL includes automatic backups
- Manual backup: `heroku pg:backups:capture`
- Download backup: `heroku pg:backups:download`

## Cost Optimization

### Free Tier Limitations
- Apps sleep after 30 minutes of inactivity
- 550-1000 free dyno hours per month
- PostgreSQL limited to 10,000 rows

### Paid Plans
- Basic dyno: $7/month (no sleeping)
- Standard PostgreSQL: $9/month (10M rows)
- Production PostgreSQL: $50/month (unlimited)

## Next Steps

After deployment:
1. Test all functionality
2. Set up monitoring and alerts
3. Configure custom domain (if needed)
4. Set up CI/CD pipeline
5. Monitor performance and costs

## Support

- Heroku Documentation: [devcenter.heroku.com](https://devcenter.heroku.com)
- Vapor Documentation: [docs.vapor.codes](https://docs.vapor.codes)
- MatchIQ Issues: [GitHub Issues](https://github.com/your-username/MatchIQ/issues)

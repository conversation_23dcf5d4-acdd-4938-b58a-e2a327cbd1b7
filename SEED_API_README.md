# MatchIQ Seed API

The MatchIQ application now includes a comprehensive API for managing seed data. This is much more convenient than command-line tools for development and testing.

## 🌐 Web Interface (Recommended)

Visit the admin panel for an easy-to-use interface:
```
http://127.0.0.1:8080/admin/seed
```

The web interface provides:
- ✅ Current database status
- ✅ One-click seeding options
- ✅ Progress indicators
- ✅ Success/error notifications
- ✅ Confirmation dialogs for destructive actions

## 🔌 API Endpoints

### Get Database Status
```http
GET /api/seed
```

**Response:**
```json
{
  "clients": 0,
  "caregivers": 0,
  "questions": 10,
  "clientResponses": 0,
  "caregiverResponses": 0,
  "message": "Database status retrieved successfully"
}
```

### Seed All Data
```http
POST /api/seed/all
```

Creates 30 clients and 30 caregivers with realistic responses.

**Response:**
```json
{
  "success": true,
  "message": "Successfully seeded 30 clients and 30 caregivers",
  "clientsCreated": 30,
  "caregiversCreated": 30,
  "duration": 2.45
}
```

### Seed Clients Only
```http
POST /api/seed/clients
```

Creates 30 sample clients with questionnaire responses.

### Seed Caregivers Only
```http
POST /api/seed/caregivers
```

Creates 30 sample caregivers with questionnaire responses.

### Clear All Data
```http
DELETE /api/seed/clear
```

⚠️ **Warning:** Permanently deletes all clients, caregivers, and responses.

### Reset and Seed
```http
POST /api/seed/reset
```

Clears existing data and creates fresh sample data in one operation.

## 🚀 Quick Usage Examples

### Using curl

**Check status:**
```bash
curl http://127.0.0.1:8080/api/seed
```

**Seed all data:**
```bash
curl -X POST http://127.0.0.1:8080/api/seed/all
```

**Reset and seed:**
```bash
curl -X POST http://127.0.0.1:8080/api/seed/reset
```

**Clear all data:**
```bash
curl -X DELETE http://127.0.0.1:8080/api/seed/clear
```

### Using JavaScript (Frontend)

```javascript
// Check database status
const status = await fetch('/api/seed').then(r => r.json());
console.log(`Database has ${status.clients} clients and ${status.caregivers} caregivers`);

// Seed all data
const result = await fetch('/api/seed/all', { method: 'POST' }).then(r => r.json());
if (result.success) {
    console.log(result.message);
}

// Reset database
const reset = await fetch('/api/seed/reset', { method: 'POST' }).then(r => r.json());
```

## 📊 Sample Data Overview

### Clients (30 total)
- **Names**: Emma Johnson, Michael Chen, Sarah Williams, etc.
- **Care Needs**: Personal Care, Companionship, Medication Management
- **Schedules**: Morning, Afternoon, Evening, Flexible
- **Locations**: Downtown, Suburbs, North/South/East/West Side
- **Special Needs**: Dementia care, Mobility assistance, etc.
- **Budgets**: $15-20/hour to $35+/hour

### Caregivers (30 total)
- **Names**: Grace Patterson, Marcus Johnson, Isabella Rodriguez, etc.
- **Experience**: 0-1 years to 10+ years, certifications
- **Services**: Matching client care needs
- **Availability**: Complementary to client schedules
- **Coverage**: Geographic areas matching client preferences
- **Specializations**: Matching client special needs

## 🎯 Perfect for Testing

The seed data is designed to create realistic matching scenarios:
- **High Compatibility**: Some clients/caregivers have excellent matches
- **Varied Responses**: Different combinations for edge case testing
- **Realistic Distribution**: Balanced across all preference categories
- **Professional Quality**: Names and responses feel authentic

## 🔧 Integration with Development

### Automated Testing
```javascript
// Reset database before each test suite
beforeEach(async () => {
    await fetch('/api/seed/reset', { method: 'POST' });
});
```

### Development Workflow
1. **Start fresh**: Use reset endpoint for clean state
2. **Test features**: Use seeded data to test matching, filtering, etc.
3. **Add specific cases**: Use individual endpoints to add targeted data
4. **Monitor status**: Check endpoint to see current data state

## 🛡️ Safety Features

- **Confirmation dialogs** in web interface for destructive actions
- **Detailed responses** with success/error status
- **Duration tracking** to monitor performance
- **Foreign key handling** to maintain data integrity
- **Error handling** with descriptive messages

## 🔗 Quick Links

- **Admin Interface**: http://127.0.0.1:8080/admin/seed
- **View Clients**: http://127.0.0.1:8080/clients
- **View Caregivers**: http://127.0.0.1:8080/caregivers
- **API Status**: http://127.0.0.1:8080/api/seed

## 🆚 Advantages over Command Line

✅ **Web Interface**: Visual, user-friendly interface  
✅ **Real-time Feedback**: Progress indicators and notifications  
✅ **API Integration**: Easy to integrate with frontend/testing  
✅ **No Terminal Required**: Works from any browser  
✅ **Status Monitoring**: See current database state at a glance  
✅ **Safer Operations**: Confirmation dialogs prevent accidents  
✅ **Better Error Handling**: Clear error messages and recovery  

The API approach is much more convenient for development, testing, and demonstration purposes!

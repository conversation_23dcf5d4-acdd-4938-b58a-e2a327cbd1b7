/* MatchIQ - Modern Mobile-First CSS */

:root {
    --primary-color: #4361ee;
    --primary-dark: #3730a3;
    --primary-light: #6366f1;
    --success-color: #10b981;
    --info-color: #06b6d4;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 0.75rem;
    --border-radius-sm: 0.5rem;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    font-size: 16px;
}

/* Layout */
.main-content {
    min-height: calc(100vh - 120px);
    padding-top: 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: 1rem;
}

.display-4 {
    font-weight: 700;
    letter-spacing: -0.025em;
}

.lead {
    font-size: 1.125rem;
    color: var(--gray-600);
}

/* Buttons */
.btn {
    font-weight: 500;
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease-in-out;
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    background: white;
    overflow: hidden;
}

.card-body {
    padding: 1.5rem;
}

.card.shadow-lg {
    box-shadow: var(--shadow-xl);
}

/* Category Icons */
.category-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.25rem;
    flex-shrink: 0;
}

/* Match Process Steps */
.match-process {
    padding: 1.5rem 0;
}

.step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
    box-shadow: var(--shadow);
}

.step div:last-child {
    font-size: 0.95rem;
    color: var(--gray-700);
    line-height: 1.5;
}

/* Question categories styling */
.question-card {
    border-left: 4px solid var(--gray-200);
    margin-bottom: 1.5rem;
    transition: all 0.2s ease-in-out;
}

.question-card:hover {
    box-shadow: var(--shadow-lg);
}

.question-card.personality {
    border-left-color: var(--primary-color);
}

.question-card.care-needs {
    border-left-color: var(--success-color);
}

.question-card.lifestyle {
    border-left-color: var(--info-color);
}

.question-card.cultural {
    border-left-color: var(--warning-color);
}

.question-card.logistics {
    border-left-color: var(--danger-color);
}

/* Form section styling */
.form-section {
    padding: 2rem;
    margin-bottom: 2rem;
    border-radius: var(--border-radius);
    background-color: white;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-100);
}

/* Form Controls */
.form-control {
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--gray-300);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.2s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

/* Option selection styling */
.option-check {
    cursor: pointer;
    padding: 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    margin-bottom: 0.75rem;
    transition: all 0.2s ease-in-out;
    background: white;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.option-check:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-300);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.option-check.selected {
    background-color: rgba(67, 97, 238, 0.05);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.option-check input[type="checkbox"],
.option-check input[type="radio"] {
    margin: 0;
    transform: scale(1.2);
}

/* List Groups */
.list-group-item {
    border: none;
    padding: 1.25rem;
    background: white;
    border-radius: var(--border-radius-sm) !important;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease-in-out;
}

.list-group-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.list-group-item:first-child {
    border-top-left-radius: var(--border-radius-sm) !important;
    border-top-right-radius: var(--border-radius-sm) !important;
}

.list-group-item:last-child {
    border-bottom-left-radius: var(--border-radius-sm) !important;
    border-bottom-right-radius: var(--border-radius-sm) !important;
}

/* Navigation */
.navbar {
    box-shadow: var(--shadow);
    border-bottom: 1px solid var(--gray-200);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
}

.nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9) !important;
    transition: color 0.2s ease-in-out;
}

.nav-link:hover {
    color: white !important;
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius-sm);
    padding: 0.5rem;
}

.dropdown-item {
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.dropdown-item:hover {
    background-color: var(--gray-100);
    color: var(--gray-900);
}

/* Footer */
.footer {
    background-color: white !important;
    border-top: 1px solid var(--gray-200);
    box-shadow: 0 -1px 3px 0 rgb(0 0 0 / 0.1);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    font-weight: 500;
}

.alert-danger {
    background-color: #fef2f2;
    color: #991b1b;
}

.alert-info {
    background-color: #f0f9ff;
    color: #1e40af;
}

.alert-success {
    background-color: #f0fdf4;
    color: #166534;
}

/* Loading States */
.spinner-border {
    color: var(--primary-color);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .main-content {
        padding-top: 0.5rem;
    }

    .container-fluid {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    .display-4 {
        font-size: 2rem;
    }

    .btn-lg {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .form-section {
        padding: 1.5rem;
    }

    .category-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .step-number {
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
    }

    .option-check {
        padding: 0.75rem;
    }

    .list-group-item {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .d-grid.gap-2.d-md-flex {
        display: grid !important;
        gap: 0.5rem !important;
    }

    .step {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .step-number {
        align-self: center;
    }

    .hero-content h1 {
        font-size: 1.75rem;
    }

    .matching-criteria .list-group-item {
        padding: 0.75rem;
    }

    .category-icon {
        width: 36px;
        height: 36px;
        font-size: 0.875rem;
    }

    .navbar-brand {
        font-size: 1.25rem;
    }

    .card-body.p-4.p-md-5 {
        padding: 1.5rem !important;
    }
}

/* Additional utility classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.shadow-soft {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* Improved focus states for accessibility */
.btn:focus,
.form-control:focus,
.option-check:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading animation improvements */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading-pulse {
    animation: pulse 2s infinite;
}

/* Smooth transitions for all interactive elements */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Match Interface Styles */
.match-percentage-badge {
    text-align: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    padding: 1rem;
    backdrop-filter: blur(10px);
}

/* Match Banner Styles */
.match-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    color: white;
    box-shadow: var(--shadow-lg);
    margin-bottom: 2rem;
}

.match-banner-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.match-banner-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.match-banner-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    backdrop-filter: blur(10px);
}

.match-banner-text {
    flex: 1;
}

.match-banner-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    color: white;
}

.match-banner-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 0.95rem;
}

.match-banner-percentage {
    text-align: center;
    background: rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    backdrop-filter: blur(10px);
    min-width: 120px;
}

.match-percentage-value {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.match-percentage-label {
    display: block;
    font-size: 0.875rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.compatibility-item {
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    transition: all 0.2s ease-in-out;
}

.compatibility-item:hover {
    background: white;
    box-shadow: var(--shadow-sm);
}

.accordion-item {
    border-radius: var(--border-radius-sm) !important;
    overflow: hidden;
}

.accordion-button {
    background: var(--gray-50);
    border: none;
    font-weight: 500;
    padding: 1rem 1.25rem;
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.accordion-body {
    background: white;
    border-top: 1px solid var(--gray-200);
}

/* Match navigation buttons */
.match-nav-btn {
    min-height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}

/* Action section improvements */
.action-section {
    background: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 2rem;
}

.primary-actions .btn {
    min-height: 3.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
}

.primary-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.secondary-actions {
    border-top: 1px solid var(--gray-200);
    padding-top: 1rem;
    margin-top: 1rem;
}

.secondary-actions .btn {
    min-height: 2.5rem;
    font-weight: 500;
}

/* Confirmation status badges */
.status-confirmed {
    background: linear-gradient(135deg, var(--success-color), #34d399);
    color: white;
}

.status-rejected {
    background: linear-gradient(135deg, var(--danger-color), #f87171);
    color: white;
}

.status-pending {
    background: linear-gradient(135deg, var(--warning-color), #fbbf24);
    color: white;
}

/* Modal improvements */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
}

.modal-header {
    border-bottom: 1px solid var(--gray-200);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--gray-200);
    padding: 1.5rem;
}

/* Progress bars in match view */
.progress {
    background-color: var(--gray-200);
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Responsive adjustments for match view */
@media (max-width: 768px) {
    .match-percentage-badge {
        padding: 0.75rem;
    }

    .match-percentage-badge .display-6 {
        font-size: 1.5rem;
    }

    .compatibility-item {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .accordion-button {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .modal-dialog {
        margin: 1rem;
    }

    /* Match banner responsive */
    .match-banner {
        padding: 1rem;
    }

    .match-banner-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .match-banner-info {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .match-banner-name {
        font-size: 1.25rem;
    }

    .match-percentage-value {
        font-size: 1.75rem;
    }

    .match-banner-percentage {
        min-width: 100px;
        padding: 0.75rem 1rem;
    }
}

@media (max-width: 576px) {
    .match-nav-btn {
        min-height: 2.5rem;
        font-size: 0.875rem;
    }

    .compatibility-item .badge {
        font-size: 0.75rem;
    }

    .progress {
        height: 6px !important;
    }
}

/* Print styles */
@media print {
    .navbar, .footer, .btn {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .modal {
        display: none !important;
    }
}

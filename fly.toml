# fly.toml app configuration file generated for novic on 2025-05-25T22:13:06Z
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'novic'
primary_region = 'mia'

[build]

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
  memory_mb = 1024
